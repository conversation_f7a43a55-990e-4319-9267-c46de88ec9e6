import React, { useRef, useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Upload, X, File, Image, FileText } from 'lucide-react';

interface FileUploadProps {
  value: string | string[] | null;
  onChange: (value: string | string[] | null) => void;
  accept?: string;
  multiple?: boolean;
  hasError?: boolean;
  maxSize?: number; // in MB
}

interface FileInfo {
  name: string;
  size: number;
  type: string;
  url: string;
}

export const FileUpload: React.FC<FileUploadProps> = ({
  value,
  onChange,
  accept,
  multiple = false,
  hasError = false,
  maxSize = 10, // 10MB default
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [dragOver, setDragOver] = useState(false);
  const [files, setFiles] = useState<FileInfo[]>([]);

  const getFileIcon = (type: string) => {
    if (type.startsWith('image/')) return <Image className="h-4 w-4" />;
    if (type.includes('text/') || type.includes('json') || type.includes('xml')) return <FileText className="h-4 w-4" />;
    return <File className="h-4 w-4" />;
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleFileSelect = (selectedFiles: FileList) => {
    const fileArray = Array.from(selectedFiles);
    
    // Validate file size
    const oversizedFiles = fileArray.filter(file => file.size > maxSize * 1024 * 1024);
    if (oversizedFiles.length > 0) {
      alert(`Files too large. Maximum size is ${maxSize}MB.`);
      return;
    }

    // Process files
    const processedFiles: FileInfo[] = fileArray.map(file => ({
      name: file.name,
      size: file.size,
      type: file.type,
      url: URL.createObjectURL(file),
    }));

    if (multiple) {
      const newFiles = [...files, ...processedFiles];
      setFiles(newFiles);
      onChange(newFiles.map(f => f.url));
    } else {
      setFiles(processedFiles);
      onChange(processedFiles[0]?.url || null);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length > 0) {
      handleFileSelect(droppedFiles);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const removeFile = (index: number) => {
    const newFiles = files.filter((_, i) => i !== index);
    setFiles(newFiles);
    
    if (multiple) {
      onChange(newFiles.length > 0 ? newFiles.map(f => f.url) : null);
    } else {
      onChange(null);
    }
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  return (
    <Card className={`p-0 ${hasError ? 'border-red-500' : ''}`}>
      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        multiple={multiple}
        onChange={(e) => e.target.files && handleFileSelect(e.target.files)}
        className="hidden"
      />

      {/* Drop zone */}
      <div
        className={`
          border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors
          ${dragOver ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'}
          ${hasError ? 'border-red-500' : ''}
        `}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={openFileDialog}
      >
        <Upload className="h-8 w-8 mx-auto text-gray-400 mb-2" />
        <div className="text-sm text-gray-600 mb-2">
          {dragOver ? 'Drop files here' : 'Click to upload or drag and drop'}
        </div>
        <div className="text-xs text-gray-500">
          {accept && `Accepted formats: ${accept}`}
          {maxSize && ` • Max size: ${maxSize}MB`}
          {multiple && ' • Multiple files allowed'}
        </div>
      </div>

      {/* File list */}
      {files.length > 0 && (
        <div className="border-t p-3 space-y-2">
          <div className="text-sm font-medium text-gray-700">
            {multiple ? `${files.length} file(s) selected` : 'Selected file'}
          </div>
          
          {files.map((file, index) => (
            <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
              <div className="flex items-center space-x-2 flex-1 min-w-0">
                {getFileIcon(file.type)}
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-gray-900 truncate">
                    {file.name}
                  </div>
                  <div className="text-xs text-gray-500">
                    {formatFileSize(file.size)}
                  </div>
                </div>
                <Badge variant="outline" className="text-xs">
                  {file.type.split('/')[1]?.toUpperCase() || 'FILE'}
                </Badge>
              </div>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  removeFile(index);
                }}
                className="ml-2 h-6 w-6 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          ))}
        </div>
      )}

      {/* Footer */}
      <div className="border-t bg-gray-50 px-3 py-2 text-xs text-gray-600">
        <div className="flex justify-between items-center">
          <span>
            {files.length === 0 ? 'No files selected' : `${files.length} file(s)`}
          </span>
          {files.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                setFiles([]);
                onChange(null);
              }}
              className="h-5 px-2 text-xs"
            >
              Clear all
            </Button>
          )}
        </div>
      </div>
    </Card>
  );
};
