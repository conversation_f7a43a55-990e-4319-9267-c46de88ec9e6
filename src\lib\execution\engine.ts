// Workflow Execution Engine
// This file contains the core workflow execution logic

import {
  WorkflowExecutor,
  EnhancedWorkflow,
  ExecutionContext,
  WorkflowExecutionResult,
  NodeExecutionResult,
  WorkflowNode,
  ValidationResult,
  ExecutionEvent,
  ExecutionProgress
} from '@/types/workflow';
import { nodeRegistry } from '@/lib/nodes/registry';
import { executionWebSocketService } from '@/lib/websocket/ExecutionWebSocketService';

export class WorkflowExecutionEngine implements WorkflowExecutor {
  private static instance: WorkflowExecutionEngine;
  private activeExecutions: Map<string, any> = new Map();
  private executionSubscriptions: Map<string, ((event: ExecutionEvent) => void)[]> = new Map();

  private constructor() {}

  public static getInstance(): WorkflowExecutionEngine {
    if (!WorkflowExecutionEngine.instance) {
      WorkflowExecutionEngine.instance = new WorkflowExecutionEngine();
    }
    return WorkflowExecutionEngine.instance;
  }

  public async execute(
    workflow: EnhancedWorkflow,
    context: ExecutionContext
  ): Promise<WorkflowExecutionResult> {
    const startTime = new Date();
    context.startTime = startTime;
    context.status = 'running';

    // Initialize execution tracking
    this.activeExecutions.set(context.executionId, {
      workflowId: workflow.id,
      status: 'running',
      startTime: startTime.getTime(),
      completedNodes: 0,
      totalNodes: workflow.nodes.length,
      currentNode: null,
    });

    // Emit workflow started event
    this.emitExecutionEvent({
      type: 'workflow_started',
      executionId: context.executionId,
      workflowId: workflow.id,
      timestamp: new Date(),
      data: { workflow: { id: workflow.id, name: workflow.name } },
    });

    const result: WorkflowExecutionResult = {
      success: false,
      executionId: context.executionId,
      startTime,
      endTime: new Date(),
      duration: 0,
      nodeResults: {},
      nodesExecuted: 0,
      totalNodes: workflow.nodes.length,
    };

    try {
      // Validate workflow before execution
      const validation = this.validateWorkflow(workflow);
      if (!validation.valid) {
        throw new Error(`Workflow validation failed: ${validation.errors.map(e => e.message).join(', ')}`);
      }

      // Find trigger nodes (starting points)
      const triggerNodes = workflow.nodes.filter(node => {
        const nodeType = nodeRegistry.getNode(node.type);
        return nodeType?.type === 'trigger';
      });

      if (triggerNodes.length === 0) {
        throw new Error('No trigger nodes found in workflow');
      }

      // Execute workflow starting from trigger nodes
      const executionOrder = this.calculateExecutionOrder(workflow, triggerNodes);
      
      for (const nodeId of executionOrder) {
        const node = workflow.nodes.find(n => n.id === nodeId);
        if (!node || node.disabled) continue;

        context.currentNodeId = nodeId;

        // Update execution progress
        this.updateExecutionProgress(context.executionId, {
          currentNode: nodeId,
        });

        // Emit node started event
        this.emitExecutionEvent({
          type: 'node_started',
          executionId: context.executionId,
          workflowId: workflow.id,
          nodeId,
          timestamp: new Date(),
          data: { node: { id: node.id, name: node.name, type: node.type } },
        });

        try {
          // Get input data from previous nodes
          const inputData = this.getNodeInputData(node, workflow, result.nodeResults);

          // Execute the node
          const nodeResult = await this.executeNode(node, inputData, context);
          result.nodeResults[nodeId] = nodeResult;
          result.nodesExecuted++;

          // Emit node completed event
          this.emitExecutionEvent({
            type: nodeResult.success ? 'node_completed' : 'node_failed',
            executionId: context.executionId,
            workflowId: workflow.id,
            nodeId,
            timestamp: new Date(),
            data: {
              node: { id: node.id, name: node.name, type: node.type },
              result: nodeResult,
            },
          });

          // Update execution progress
          this.updateExecutionProgress(context.executionId, {
            completedNodes: result.nodesExecuted,
          });

          // If node failed and shouldn't continue on fail, stop execution
          if (!nodeResult.success && !node.continueOnFail) {
            throw new Error(`Node ${node.name} failed: ${nodeResult.error?.message}`);
          }

        } catch (error) {
          const nodeResult: NodeExecutionResult = {
            success: false,
            error: {
              message: error instanceof Error ? error.message : 'Unknown error',
              stack: error instanceof Error ? error.stack : undefined,
            },
            executionTime: 0,
          };
          
          result.nodeResults[nodeId] = nodeResult;
          
          if (!node.continueOnFail) {
            throw error;
          }
        }
      }

      // Execution completed successfully
      result.success = true;
      context.status = 'success';

      // Update execution status
      this.updateExecutionProgress(context.executionId, {
        status: 'success',
      });

      // Emit workflow completed event
      this.emitExecutionEvent({
        type: 'workflow_completed',
        executionId: context.executionId,
        workflowId: workflow.id,
        timestamp: new Date(),
        data: {
          result,
          duration: result.duration,
          nodesExecuted: result.nodesExecuted,
        },
      });

    } catch (error) {
      result.success = false;
      result.error = {
        message: error instanceof Error ? error.message : 'Unknown error',
        nodeId: context.currentNodeId,
        stack: error instanceof Error ? error.stack : undefined,
      };
      context.status = 'error';

      // Update execution status
      this.updateExecutionProgress(context.executionId, {
        status: 'error',
      });

      // Emit workflow failed event
      this.emitExecutionEvent({
        type: 'workflow_failed',
        executionId: context.executionId,
        workflowId: workflow.id,
        timestamp: new Date(),
        data: {
          error: result.error,
          nodesExecuted: result.nodesExecuted,
        },
      });
    }

    const endTime = new Date();
    result.endTime = endTime;
    result.duration = endTime.getTime() - startTime.getTime();
    context.endTime = endTime;

    // Clean up execution tracking
    this.activeExecutions.delete(context.executionId);

    return result;
  }

  public async executeNode(
    node: WorkflowNode, 
    input: any, 
    context: ExecutionContext
  ): Promise<NodeExecutionResult> {
    const startTime = Date.now();
    
    try {
      const nodeType = nodeRegistry.getNode(node.type);
      if (!nodeType) {
        throw new Error(`Unknown node type: ${node.type}`);
      }

      // Apply timeout if specified
      const timeout = node.waitBetween || nodeType.timeout || context.settings.timeout || 30000;
      
      const executeWithTimeout = async (): Promise<NodeExecutionResult> => {
        // Execute based on node type
        switch (nodeType.id) {
          case 'webhook-trigger':
            return this.executeWebhookTrigger(node, input, context);
          case 'schedule-trigger':
            return this.executeScheduleTrigger(node, input, context);
          case 'manual-trigger':
            return this.executeManualTrigger(node, input, context);
          case 'http-request':
            return this.executeHttpRequest(node, input, context);
          default:
            throw new Error(`Execution not implemented for node type: ${nodeType.id}`);
        }
      };

      const result = await Promise.race([
        executeWithTimeout(),
        new Promise<NodeExecutionResult>((_, reject) => 
          setTimeout(() => reject(new Error('Node execution timeout')), timeout)
        ),
      ]);

      result.executionTime = Date.now() - startTime;
      return result;

    } catch (error) {
      const result: NodeExecutionResult = {
        success: false,
        error: {
          message: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined,
        },
        executionTime: Date.now() - startTime,
      };

      // Handle retry logic
      if (node.retryOnFail && nodeRegistry.getNode(node.type)?.retryPolicy?.enabled) {
        return this.retry(node, context);
      }

      return result;
    }
  }

  public async handleError(
    error: Error, 
    node: WorkflowNode, 
    context: ExecutionContext
  ): Promise<void> {
    // Log error
    console.error(`Error in node ${node.name} (${node.id}):`, error);
    
    // Could implement error workflow execution here
    // Could send notifications, log to external systems, etc.
  }

  public async retry(
    node: WorkflowNode, 
    context: ExecutionContext
  ): Promise<NodeExecutionResult> {
    const nodeType = nodeRegistry.getNode(node.type);
    const retryPolicy = nodeType?.retryPolicy;
    
    if (!retryPolicy?.enabled) {
      throw new Error('Retry not enabled for this node type');
    }

    let lastError: Error | undefined;
    
    for (let attempt = 1; attempt <= retryPolicy.maxAttempts; attempt++) {
      try {
        if (attempt > 1) {
          // Calculate delay with backoff
          const delay = retryPolicy.delay * Math.pow(retryPolicy.backoffMultiplier || 1, attempt - 1);
          const maxDelay = retryPolicy.maxDelay || 30000;
          const actualDelay = Math.min(delay, maxDelay);
          
          await new Promise(resolve => setTimeout(resolve, actualDelay));
        }

        // Try to execute the node again
        const result = await this.executeNode(node, null, context);
        if (result.success) {
          return result;
        }
        
        lastError = new Error(result.error?.message || 'Node execution failed');
        
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
      }
    }

    // All retries failed
    return {
      success: false,
      error: {
        message: `All ${retryPolicy.maxAttempts} retry attempts failed. Last error: ${lastError?.message}`,
        stack: lastError?.stack,
      },
      executionTime: 0,
    };
  }

  public validateWorkflow(workflow: EnhancedWorkflow): ValidationResult {
    const errors: Array<{nodeId?: string, message: string, type: 'error' | 'warning'}> = [];

    // Check for at least one trigger node
    const triggerNodes = workflow.nodes.filter(node => {
      const nodeType = nodeRegistry.getNode(node.type);
      return nodeType?.type === 'trigger';
    });

    if (triggerNodes.length === 0) {
      errors.push({
        message: 'Workflow must have at least one trigger node',
        type: 'error',
      });
    }

    // Validate each node
    workflow.nodes.forEach(node => {
      const nodeType = nodeRegistry.getNode(node.type);
      if (!nodeType) {
        errors.push({
          nodeId: node.id,
          message: `Unknown node type: ${node.type}`,
          type: 'error',
        });
        return;
      }

      // Validate required properties
      nodeType.properties.forEach(prop => {
        if (prop.required && !node.parameters[prop.name]) {
          errors.push({
            nodeId: node.id,
            message: `Required property '${prop.displayName}' is missing`,
            type: 'error',
          });
        }
      });
    });

    // Check for circular dependencies
    if (this.hasCircularDependencies(workflow)) {
      errors.push({
        message: 'Workflow contains circular dependencies',
        type: 'error',
      });
    }

    return {
      valid: errors.filter(e => e.type === 'error').length === 0,
      errors,
    };
  }

  // Private helper methods
  private calculateExecutionOrder(workflow: EnhancedWorkflow, startNodes: WorkflowNode[]): string[] {
    const visited = new Set<string>();
    const order: string[] = [];

    const visit = (nodeId: string) => {
      if (visited.has(nodeId)) return;
      visited.add(nodeId);
      order.push(nodeId);

      // Find connected nodes
      const outgoingEdges = workflow.edges.filter(edge => edge.source === nodeId);
      outgoingEdges.forEach(edge => {
        visit(edge.target);
      });
    };

    startNodes.forEach(node => visit(node.id));
    return order;
  }

  private getNodeInputData(
    node: WorkflowNode, 
    workflow: EnhancedWorkflow, 
    nodeResults: Record<string, NodeExecutionResult>
  ): any {
    const incomingEdges = workflow.edges.filter(edge => edge.target === node.id);
    
    if (incomingEdges.length === 0) {
      return null; // Trigger node or isolated node
    }

    // For now, just return the data from the first incoming node
    // In a more advanced implementation, this would handle multiple inputs
    const firstEdge = incomingEdges[0];
    const sourceResult = nodeResults[firstEdge.source];
    
    return sourceResult?.outputData || sourceResult?.data;
  }

  private hasCircularDependencies(workflow: EnhancedWorkflow): boolean {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const hasCycle = (nodeId: string): boolean => {
      if (recursionStack.has(nodeId)) return true;
      if (visited.has(nodeId)) return false;

      visited.add(nodeId);
      recursionStack.add(nodeId);

      const outgoingEdges = workflow.edges.filter(edge => edge.source === nodeId);
      for (const edge of outgoingEdges) {
        if (hasCycle(edge.target)) return true;
      }

      recursionStack.delete(nodeId);
      return false;
    };

    return workflow.nodes.some(node => hasCycle(node.id));
  }

  // Node execution implementations
  private async executeWebhookTrigger(
    node: WorkflowNode, 
    input: any, 
    context: ExecutionContext
  ): Promise<NodeExecutionResult> {
    // Webhook triggers are handled externally, this just passes through the data
    return {
      success: true,
      data: input,
      outputData: input,
      executionTime: 0,
    };
  }

  private async executeScheduleTrigger(
    node: WorkflowNode, 
    input: any, 
    context: ExecutionContext
  ): Promise<NodeExecutionResult> {
    // Schedule triggers are handled externally, this just passes through the data
    return {
      success: true,
      data: { timestamp: new Date(), scheduledExecution: true },
      outputData: { timestamp: new Date(), scheduledExecution: true },
      executionTime: 0,
    };
  }

  private async executeManualTrigger(
    node: WorkflowNode, 
    input: any, 
    context: ExecutionContext
  ): Promise<NodeExecutionResult> {
    const inputData = node.parameters.inputData ? JSON.parse(node.parameters.inputData) : {};
    
    return {
      success: true,
      data: { ...inputData, manualExecution: true, timestamp: new Date() },
      outputData: { ...inputData, manualExecution: true, timestamp: new Date() },
      executionTime: 0,
    };
  }

  private async executeHttpRequest(
    node: WorkflowNode, 
    input: any, 
    context: ExecutionContext
  ): Promise<NodeExecutionResult> {
    const { method, url, headers, body } = node.parameters;
    
    try {
      const requestOptions: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
          ...JSON.parse(headers || '{}'),
        },
      };

      if (['POST', 'PUT', 'PATCH'].includes(method) && body) {
        requestOptions.body = JSON.stringify(JSON.parse(body));
      }

      const response = await fetch(url, requestOptions);
      const responseData = await response.json();

      return {
        success: response.ok,
        data: responseData,
        outputData: {
          statusCode: response.status,
          headers: Object.fromEntries(response.headers.entries()),
          body: responseData,
        },
        executionTime: 0,
      };

    } catch (error) {
      return {
        success: false,
        error: {
          message: error instanceof Error ? error.message : 'HTTP request failed',
          stack: error instanceof Error ? error.stack : undefined,
        },
        executionTime: 0,
      };
    }
  }

  // Real-time execution methods
  public subscribeToExecution(executionId: string, callback: (event: ExecutionEvent) => void): () => void {
    if (!this.executionSubscriptions.has(executionId)) {
      this.executionSubscriptions.set(executionId, []);
    }

    this.executionSubscriptions.get(executionId)!.push(callback);

    // Return unsubscribe function
    return () => {
      const callbacks = this.executionSubscriptions.get(executionId);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index > -1) {
          callbacks.splice(index, 1);
          if (callbacks.length === 0) {
            this.executionSubscriptions.delete(executionId);
          }
        }
      }
    };
  }

  public async getExecutionProgress(executionId: string): Promise<ExecutionProgress | null> {
    const execution = this.activeExecutions.get(executionId);
    if (!execution) {
      return null;
    }

    const now = Date.now();
    const duration = now - execution.startTime;
    const percentage = execution.totalNodes > 0 ? (execution.completedNodes / execution.totalNodes) * 100 : 0;

    // Simple estimation based on current progress
    const estimatedTimeRemaining = execution.completedNodes > 0
      ? (duration / execution.completedNodes) * (execution.totalNodes - execution.completedNodes)
      : undefined;

    return {
      executionId,
      status: execution.status,
      completedNodes: execution.completedNodes,
      totalNodes: execution.totalNodes,
      currentNode: execution.currentNode,
      percentage: Math.round(percentage),
      duration,
      estimatedTimeRemaining,
    };
  }

  private emitExecutionEvent(event: ExecutionEvent): void {
    // Emit to local subscribers
    const callbacks = this.executionSubscriptions.get(event.executionId);
    if (callbacks) {
      callbacks.forEach(callback => {
        try {
          callback(event);
        } catch (error) {
          console.error('Error in execution event callback:', error);
        }
      });
    }

    // Emit to WebSocket service for broadcasting
    try {
      executionWebSocketService.connect().then(() => {
        // WebSocket service will handle broadcasting to connected clients
      }).catch(error => {
        console.warn('Failed to connect to WebSocket service:', error);
      });
    } catch (error) {
      console.warn('WebSocket service not available:', error);
    }
  }

  private updateExecutionProgress(
    executionId: string,
    updates: Partial<{
      status: string;
      completedNodes: number;
      currentNode: string;
    }>
  ): void {
    const execution = this.activeExecutions.get(executionId);
    if (execution) {
      Object.assign(execution, updates);

      // Emit progress event
      this.emitExecutionEvent({
        type: 'execution_progress',
        executionId,
        workflowId: execution.workflowId,
        timestamp: new Date(),
        data: {
          completedNodes: execution.completedNodes,
          totalNodes: execution.totalNodes,
          currentNode: execution.currentNode,
          percentage: execution.totalNodes > 0 ? (execution.completedNodes / execution.totalNodes) * 100 : 0,
        },
      });
    }
  }
}

// Export singleton instance
export const workflowEngine = WorkflowExecutionEngine.getInstance();
