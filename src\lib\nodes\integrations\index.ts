import { EnhancedNodeType } from '@/types/workflow';
import { slackNodes } from './slack';
import { githubNodes } from './github';
import { awsNodes } from './aws';
import { googleNodes } from './google';

// Integration categories
export const integrationCategories = {
  communication: {
    name: 'Communication',
    description: 'Send messages, emails, and notifications',
    icon: '💬',
    color: '#4A90E2',
  },
  development: {
    name: 'Development',
    description: 'Code repositories, CI/CD, and development tools',
    icon: '⚙️',
    color: '#7B68EE',
  },
  cloudStorage: {
    name: 'Cloud Storage',
    description: 'File storage and management services',
    icon: '☁️',
    color: '#32CD32',
  },
  productivity: {
    name: 'Productivity',
    description: 'Office tools, spreadsheets, and documents',
    icon: '📊',
    color: '#FF6347',
  },
  compute: {
    name: 'Compute',
    description: 'Serverless functions and computing services',
    icon: '🖥️',
    color: '#9370DB',
  },
  database: {
    name: 'Database',
    description: 'Database operations and data management',
    icon: '🗄️',
    color: '#20B2AA',
  },
  analytics: {
    name: 'Analytics',
    description: 'Data analysis and reporting tools',
    icon: '📈',
    color: '#FF8C00',
  },
  ecommerce: {
    name: 'E-commerce',
    description: 'Online stores and payment processing',
    icon: '🛒',
    color: '#DC143C',
  },
  social: {
    name: 'Social Media',
    description: 'Social media platforms and content management',
    icon: '📱',
    color: '#1DA1F2',
  },
  marketing: {
    name: 'Marketing',
    description: 'Email marketing, CRM, and customer engagement',
    icon: '📢',
    color: '#FF1493',
  },
};

// Service providers
export const serviceProviders = {
  slack: {
    name: 'Slack',
    description: 'Team communication and collaboration platform',
    icon: '💬',
    color: '#4A154B',
    website: 'https://slack.com',
    category: 'communication',
  },
  github: {
    name: 'GitHub',
    description: 'Code hosting and collaboration platform',
    icon: '🐙',
    color: '#24292e',
    website: 'https://github.com',
    category: 'development',
  },
  aws: {
    name: 'Amazon Web Services',
    description: 'Cloud computing platform',
    icon: '☁️',
    color: '#FF9900',
    website: 'https://aws.amazon.com',
    category: 'cloudStorage',
  },
  google: {
    name: 'Google Workspace',
    description: 'Productivity and collaboration tools',
    icon: '🔍',
    color: '#4285F4',
    website: 'https://workspace.google.com',
    category: 'productivity',
  },
};

// All integration nodes
export const integrationNodes: EnhancedNodeType[] = [
  ...slackNodes,
  ...githubNodes,
  ...awsNodes,
  ...googleNodes,
];

// Node registry for integrations
export class IntegrationNodeRegistry {
  private static instance: IntegrationNodeRegistry;
  private nodes: Map<string, EnhancedNodeType> = new Map();
  private nodesByCategory: Map<string, EnhancedNodeType[]> = new Map();
  private nodesByProvider: Map<string, EnhancedNodeType[]> = new Map();

  private constructor() {
    this.registerNodes();
  }

  public static getInstance(): IntegrationNodeRegistry {
    if (!IntegrationNodeRegistry.instance) {
      IntegrationNodeRegistry.instance = new IntegrationNodeRegistry();
    }
    return IntegrationNodeRegistry.instance;
  }

  private registerNodes(): void {
    integrationNodes.forEach(node => {
      this.nodes.set(node.name, node);
      
      // Group by category
      if (!this.nodesByCategory.has(node.category)) {
        this.nodesByCategory.set(node.category, []);
      }
      this.nodesByCategory.get(node.category)!.push(node);

      // Group by provider (extract from tags or name)
      const provider = this.extractProvider(node);
      if (provider) {
        if (!this.nodesByProvider.has(provider)) {
          this.nodesByProvider.set(provider, []);
        }
        this.nodesByProvider.get(provider)!.push(node);
      }
    });
  }

  private extractProvider(node: EnhancedNodeType): string | null {
    // Extract provider from node name or tags
    const name = node.name.toLowerCase();
    const tags = node.tags || [];
    
    for (const provider of Object.keys(serviceProviders)) {
      if (name.includes(provider) || tags.includes(provider)) {
        return provider;
      }
    }
    
    return null;
  }

  public getNode(name: string): EnhancedNodeType | undefined {
    return this.nodes.get(name);
  }

  public getAllNodes(): EnhancedNodeType[] {
    return Array.from(this.nodes.values());
  }

  public getNodesByCategory(category: string): EnhancedNodeType[] {
    return this.nodesByCategory.get(category) || [];
  }

  public getNodesByProvider(provider: string): EnhancedNodeType[] {
    return this.nodesByProvider.get(provider) || [];
  }

  public getCategories(): string[] {
    return Array.from(this.nodesByCategory.keys());
  }

  public getProviders(): string[] {
    return Array.from(this.nodesByProvider.keys());
  }

  public searchNodes(query: string): EnhancedNodeType[] {
    const searchTerm = query.toLowerCase();
    return this.getAllNodes().filter(node => {
      return (
        node.name.toLowerCase().includes(searchTerm) ||
        node.description.toLowerCase().includes(searchTerm) ||
        node.category.toLowerCase().includes(searchTerm) ||
        (node.tags && node.tags.some(tag => tag.toLowerCase().includes(searchTerm)))
      );
    });
  }

  public getNodeStats(): {
    totalNodes: number;
    nodesByCategory: Record<string, number>;
    nodesByProvider: Record<string, number>;
  } {
    const nodesByCategory: Record<string, number> = {};
    const nodesByProvider: Record<string, number> = {};

    this.nodesByCategory.forEach((nodes, category) => {
      nodesByCategory[category] = nodes.length;
    });

    this.nodesByProvider.forEach((nodes, provider) => {
      nodesByProvider[provider] = nodes.length;
    });

    return {
      totalNodes: this.nodes.size,
      nodesByCategory,
      nodesByProvider,
    };
  }

  public validateNodeConfiguration(nodeName: string, configuration: any): {
    valid: boolean;
    errors: string[];
  } {
    const node = this.getNode(nodeName);
    if (!node) {
      return {
        valid: false,
        errors: [`Unknown node type: ${nodeName}`],
      };
    }

    const errors: string[] = [];

    // Validate required properties
    node.properties?.forEach(property => {
      if (property.required && !configuration[property.name]) {
        errors.push(`Missing required property: ${property.displayName}`);
      }

      // Validate property types and constraints
      const value = configuration[property.name];
      if (value !== undefined && value !== null) {
        switch (property.type) {
          case 'number':
            if (typeof value !== 'number') {
              errors.push(`Property ${property.displayName} must be a number`);
            } else {
              if (property.min !== undefined && value < property.min) {
                errors.push(`Property ${property.displayName} must be at least ${property.min}`);
              }
              if (property.max !== undefined && value > property.max) {
                errors.push(`Property ${property.displayName} must be at most ${property.max}`);
              }
            }
            break;
          case 'string':
            if (typeof value !== 'string') {
              errors.push(`Property ${property.displayName} must be a string`);
            } else if (property.validation) {
              property.validation.forEach(rule => {
                if (rule.type === 'pattern' && !new RegExp(rule.value).test(value)) {
                  errors.push(rule.message || `Property ${property.displayName} format is invalid`);
                }
              });
            }
            break;
          case 'boolean':
            if (typeof value !== 'boolean') {
              errors.push(`Property ${property.displayName} must be a boolean`);
            }
            break;
          case 'json':
            try {
              if (typeof value === 'string') {
                JSON.parse(value);
              }
            } catch {
              errors.push(`Property ${property.displayName} must be valid JSON`);
            }
            break;
        }
      }
    });

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}

// Export singleton instance
export const integrationRegistry = IntegrationNodeRegistry.getInstance();
