import { EnhancedNodeType } from '@/types/workflow';

export const googleNodes: EnhancedNodeType[] = [
  {
    name: 'Google Sheets Read',
    type: 'action',
    category: 'Productivity',
    description: 'Read data from a Google Sheets spreadsheet',
    version: '1.0.0',
    icon: '📊',
    color: '#34A853',
    tags: ['google', 'sheets', 'spreadsheet', 'read'],
    inputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Input',
        required: false,
      },
    ],
    outputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Output',
      },
    ],
    properties: [
      {
        name: 'credential',
        displayName: 'Credential',
        type: 'credential',
        required: true,
        description: 'Google API credentials',
      },
      {
        name: 'spreadsheetId',
        displayName: 'Spreadsheet ID',
        type: 'string',
        required: true,
        placeholder: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
        description: 'ID of the Google Sheets spreadsheet',
      },
      {
        name: 'range',
        displayName: 'Range',
        type: 'string',
        required: true,
        placeholder: 'Sheet1!A1:D10',
        description: 'Range to read (e.g., Sheet1!A1:D10)',
      },
      {
        name: 'valueRenderOption',
        displayName: 'Value Render Option',
        type: 'select',
        required: false,
        default: 'FORMATTED_VALUE',
        options: [
          { value: 'FORMATTED_VALUE', label: 'Formatted Value' },
          { value: 'UNFORMATTED_VALUE', label: 'Unformatted Value' },
          { value: 'FORMULA', label: 'Formula' },
        ],
        description: 'How values should be represented',
      },
      {
        name: 'dateTimeRenderOption',
        displayName: 'Date Time Render Option',
        type: 'select',
        required: false,
        default: 'FORMATTED_STRING',
        options: [
          { value: 'FORMATTED_STRING', label: 'Formatted String' },
          { value: 'SERIAL_NUMBER', label: 'Serial Number' },
        ],
        description: 'How dates and times should be represented',
      },
      {
        name: 'headerRow',
        displayName: 'Header Row',
        type: 'boolean',
        required: false,
        default: true,
        description: 'First row contains headers',
      },
    ],
    credentials: [
      {
        name: 'googleApi',
        required: true,
      },
    ],
    documentation: {
      description: 'Read data from Google Sheets spreadsheets with flexible range selection and formatting options.',
      examples: [
        {
          title: 'Read All Data',
          description: 'Read all data from a sheet with headers',
          configuration: {
            spreadsheetId: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
            range: 'Sheet1!A:Z',
            headerRow: true,
          },
        },
        {
          title: 'Read Specific Range',
          description: 'Read a specific range of cells',
          configuration: {
            spreadsheetId: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
            range: 'Data!B2:E100',
            headerRow: false,
            valueRenderOption: 'UNFORMATTED_VALUE',
          },
        },
      ],
    },
  },
  {
    name: 'Google Sheets Write',
    type: 'action',
    category: 'Productivity',
    description: 'Write data to a Google Sheets spreadsheet',
    version: '1.0.0',
    icon: '✏️',
    color: '#34A853',
    tags: ['google', 'sheets', 'spreadsheet', 'write'],
    inputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Input',
        required: false,
      },
    ],
    outputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Output',
      },
    ],
    properties: [
      {
        name: 'credential',
        displayName: 'Credential',
        type: 'credential',
        required: true,
        description: 'Google API credentials',
      },
      {
        name: 'spreadsheetId',
        displayName: 'Spreadsheet ID',
        type: 'string',
        required: true,
        placeholder: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
        description: 'ID of the Google Sheets spreadsheet',
      },
      {
        name: 'range',
        displayName: 'Range',
        type: 'string',
        required: true,
        placeholder: 'Sheet1!A1',
        description: 'Starting cell or range to write to',
      },
      {
        name: 'data',
        displayName: 'Data',
        type: 'json',
        required: true,
        description: 'Data to write (2D array format)',
        default: [['Header 1', 'Header 2'], ['Value 1', 'Value 2']],
      },
      {
        name: 'valueInputOption',
        displayName: 'Value Input Option',
        type: 'select',
        required: false,
        default: 'USER_ENTERED',
        options: [
          { value: 'RAW', label: 'Raw' },
          { value: 'USER_ENTERED', label: 'User Entered' },
        ],
        description: 'How input data should be interpreted',
      },
      {
        name: 'insertDataOption',
        displayName: 'Insert Data Option',
        type: 'select',
        required: false,
        default: 'OVERWRITE',
        options: [
          { value: 'OVERWRITE', label: 'Overwrite' },
          { value: 'INSERT_ROWS', label: 'Insert Rows' },
        ],
        description: 'How data should be inserted',
      },
    ],
    credentials: [
      {
        name: 'googleApi',
        required: true,
      },
    ],
  },
  {
    name: 'Google Drive Upload File',
    type: 'action',
    category: 'Cloud Storage',
    description: 'Upload a file to Google Drive',
    version: '1.0.0',
    icon: '☁️',
    color: '#4285F4',
    tags: ['google', 'drive', 'storage', 'upload'],
    inputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Input',
        required: false,
      },
    ],
    outputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Output',
      },
    ],
    properties: [
      {
        name: 'credential',
        displayName: 'Credential',
        type: 'credential',
        required: true,
        description: 'Google API credentials',
      },
      {
        name: 'file',
        displayName: 'File',
        type: 'file',
        required: true,
        description: 'File to upload',
      },
      {
        name: 'name',
        displayName: 'File Name',
        type: 'string',
        required: false,
        placeholder: 'document.pdf',
        description: 'Name for the file in Google Drive (uses original name if not specified)',
      },
      {
        name: 'parentFolderId',
        displayName: 'Parent Folder ID',
        type: 'string',
        required: false,
        placeholder: '1dyUEebJaFnWa3Z4n0BFMVAXQ7cPDqXOD',
        description: 'ID of the parent folder (uploads to root if not specified)',
      },
      {
        name: 'description',
        displayName: 'Description',
        type: 'string',
        required: false,
        placeholder: 'File description...',
        description: 'Description for the file',
        rows: 2,
      },
      {
        name: 'convert',
        displayName: 'Convert to Google Format',
        type: 'boolean',
        required: false,
        default: false,
        description: 'Convert compatible files to Google Workspace format',
      },
    ],
    credentials: [
      {
        name: 'googleApi',
        required: true,
      },
    ],
  },
  {
    name: 'Gmail Send Email',
    type: 'action',
    category: 'Communication',
    description: 'Send an email using Gmail',
    version: '1.0.0',
    icon: '📧',
    color: '#EA4335',
    tags: ['google', 'gmail', 'email', 'communication'],
    inputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Input',
        required: false,
      },
    ],
    outputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Output',
      },
    ],
    properties: [
      {
        name: 'credential',
        displayName: 'Credential',
        type: 'credential',
        required: true,
        description: 'Google API credentials',
      },
      {
        name: 'to',
        displayName: 'To',
        type: 'string',
        required: true,
        placeholder: '<EMAIL>',
        description: 'Recipient email address(es), comma-separated',
      },
      {
        name: 'cc',
        displayName: 'CC',
        type: 'string',
        required: false,
        placeholder: '<EMAIL>',
        description: 'CC email address(es), comma-separated',
      },
      {
        name: 'bcc',
        displayName: 'BCC',
        type: 'string',
        required: false,
        placeholder: '<EMAIL>',
        description: 'BCC email address(es), comma-separated',
      },
      {
        name: 'subject',
        displayName: 'Subject',
        type: 'string',
        required: true,
        placeholder: 'Email Subject',
        description: 'Email subject line',
      },
      {
        name: 'body',
        displayName: 'Body',
        type: 'string',
        required: true,
        placeholder: 'Email body content...',
        description: 'Email body content (supports HTML)',
        rows: 5,
      },
      {
        name: 'attachments',
        displayName: 'Attachments',
        type: 'file',
        required: false,
        multiple: true,
        description: 'Files to attach to the email',
      },
      {
        name: 'isHtml',
        displayName: 'HTML Content',
        type: 'boolean',
        required: false,
        default: false,
        description: 'Body contains HTML content',
      },
    ],
    credentials: [
      {
        name: 'googleApi',
        required: true,
      },
    ],
  },
];
