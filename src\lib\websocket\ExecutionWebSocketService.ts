import { ExecutionEvent, WebSocketMessage, WorkflowExecutionSubscription } from '@/types/workflow';

export class ExecutionWebSocketService {
  private static instance: ExecutionWebSocketService;
  private ws: WebSocket | null = null;
  private subscriptions: Map<string, WorkflowExecutionSubscription[]> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private isConnecting = false;

  private constructor() {}

  public static getInstance(): ExecutionWebSocketService {
    if (!ExecutionWebSocketService.instance) {
      ExecutionWebSocketService.instance = new ExecutionWebSocketService();
    }
    return ExecutionWebSocketService.instance;
  }

  public connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      if (this.isConnecting) {
        // Wait for existing connection attempt
        const checkConnection = () => {
          if (this.ws?.readyState === WebSocket.OPEN) {
            resolve();
          } else if (!this.isConnecting) {
            reject(new Error('Connection failed'));
          } else {
            setTimeout(checkConnection, 100);
          }
        };
        checkConnection();
        return;
      }

      this.isConnecting = true;
      
      // In a real app, this would be from environment config
      const wsUrl = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8080/ws/executions';
      
      try {
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          console.log('WebSocket connected');
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
          }
        };

        this.ws.onclose = () => {
          console.log('WebSocket disconnected');
          this.isConnecting = false;
          this.ws = null;
          this.attemptReconnect();
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          this.isConnecting = false;
          reject(error);
        };

      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);
    
    setTimeout(() => {
      this.connect().catch(error => {
        console.error('Reconnection failed:', error);
      });
    }, delay);
  }

  private handleMessage(message: WebSocketMessage): void {
    switch (message.type) {
      case 'execution_event':
        if (message.event) {
          this.broadcastEvent(message.event);
        }
        break;
      case 'error':
        console.error('WebSocket server error:', message.error);
        break;
      default:
        console.warn('Unknown message type:', message.type);
    }
  }

  private broadcastEvent(event: ExecutionEvent): void {
    const executionSubscriptions = this.subscriptions.get(event.executionId) || [];
    const workflowSubscriptions = this.subscriptions.get(`workflow_${event.workflowId}`) || [];
    
    [...executionSubscriptions, ...workflowSubscriptions].forEach(subscription => {
      try {
        subscription.callback(event);
      } catch (error) {
        console.error('Error in subscription callback:', error);
      }
    });
  }

  public subscribeToExecution(
    executionId: string,
    workflowId: string,
    userId: string,
    callback: (event: ExecutionEvent) => void
  ): () => void {
    const subscription: WorkflowExecutionSubscription = {
      executionId,
      workflowId,
      userId,
      callback,
    };

    // Add to execution-specific subscriptions
    if (!this.subscriptions.has(executionId)) {
      this.subscriptions.set(executionId, []);
    }
    this.subscriptions.get(executionId)!.push(subscription);

    // Send subscription message to server
    this.sendMessage({
      type: 'subscribe',
      executionId,
      workflowId,
    });

    // Return unsubscribe function
    return () => {
      this.unsubscribeFromExecution(executionId, subscription);
    };
  }

  public subscribeToWorkflow(
    workflowId: string,
    userId: string,
    callback: (event: ExecutionEvent) => void
  ): () => void {
    const subscription: WorkflowExecutionSubscription = {
      executionId: '',
      workflowId,
      userId,
      callback,
    };

    const key = `workflow_${workflowId}`;
    if (!this.subscriptions.has(key)) {
      this.subscriptions.set(key, []);
    }
    this.subscriptions.get(key)!.push(subscription);

    // Send subscription message to server
    this.sendMessage({
      type: 'subscribe',
      workflowId,
    });

    // Return unsubscribe function
    return () => {
      this.unsubscribeFromWorkflow(workflowId, subscription);
    };
  }

  private unsubscribeFromExecution(executionId: string, subscription: WorkflowExecutionSubscription): void {
    const subscriptions = this.subscriptions.get(executionId);
    if (subscriptions) {
      const index = subscriptions.indexOf(subscription);
      if (index > -1) {
        subscriptions.splice(index, 1);
        
        if (subscriptions.length === 0) {
          this.subscriptions.delete(executionId);
          
          // Send unsubscribe message to server
          this.sendMessage({
            type: 'unsubscribe',
            executionId,
          });
        }
      }
    }
  }

  private unsubscribeFromWorkflow(workflowId: string, subscription: WorkflowExecutionSubscription): void {
    const key = `workflow_${workflowId}`;
    const subscriptions = this.subscriptions.get(key);
    if (subscriptions) {
      const index = subscriptions.indexOf(subscription);
      if (index > -1) {
        subscriptions.splice(index, 1);
        
        if (subscriptions.length === 0) {
          this.subscriptions.delete(key);
          
          // Send unsubscribe message to server
          this.sendMessage({
            type: 'unsubscribe',
            workflowId,
          });
        }
      }
    }
  }

  private sendMessage(message: WebSocketMessage): void {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket not connected, message not sent:', message);
    }
  }

  public disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.subscriptions.clear();
    this.reconnectAttempts = 0;
  }

  public getConnectionStatus(): 'connecting' | 'connected' | 'disconnected' {
    if (this.isConnecting) return 'connecting';
    if (this.ws?.readyState === WebSocket.OPEN) return 'connected';
    return 'disconnected';
  }
}

// Export singleton instance
export const executionWebSocketService = ExecutionWebSocketService.getInstance();
