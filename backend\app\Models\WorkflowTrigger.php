<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WorkflowTrigger extends Model
{
    protected $fillable = [
        'workflow_id',
        'node_id',
        'type',
        'configuration',
        'is_active',
        'last_triggered_at',
        'trigger_count',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'configuration' => 'array',
        'last_triggered_at' => 'datetime',
        'trigger_count' => 'integer',
    ];

    public function workflow(): BelongsTo
    {
        return $this->belongsTo(Workflow::class);
    }

    // Scope for active triggers
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // Scope by trigger type
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    // Increment trigger count
    public function incrementTriggerCount(): void
    {
        $this->increment('trigger_count');
        $this->update(['last_triggered_at' => now()]);
    }

    // Get webhook path for webhook triggers
    public function getWebhookPath(): ?string
    {
        if ($this->type !== 'webhook') {
            return null;
        }

        return $this->configuration['path'] ?? null;
    }

    // Get cron rule for schedule triggers
    public function getCronRule(): ?string
    {
        if ($this->type !== 'schedule') {
            return null;
        }

        return $this->configuration['rule'] ?? null;
    }

    // Check if trigger is webhook type
    public function isWebhook(): bool
    {
        return $this->type === 'webhook';
    }

    // Check if trigger is schedule type
    public function isSchedule(): bool
    {
        return $this->type === 'schedule';
    }

    // Check if trigger is manual type
    public function isManual(): bool
    {
        return $this->type === 'manual';
    }

    // Get trigger configuration for specific key
    public function getConfig(string $key, $default = null)
    {
        return $this->configuration[$key] ?? $default;
    }

    // Set trigger configuration
    public function setConfig(string $key, $value): void
    {
        $config = $this->configuration ?? [];
        $config[$key] = $value;
        $this->configuration = $config;
    }
}
