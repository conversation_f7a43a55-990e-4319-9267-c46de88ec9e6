import { EnhancedNodeType } from '@/types/workflow';

export const githubNodes: EnhancedNodeType[] = [
  {
    name: 'GitHub Create Issue',
    type: 'action',
    category: 'Development',
    description: 'Create a new issue in a GitHub repository',
    version: '1.0.0',
    icon: '🐛',
    color: '#24292e',
    tags: ['github', 'issue', 'development'],
    inputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Input',
        required: false,
      },
    ],
    outputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Output',
      },
    ],
    properties: [
      {
        name: 'credential',
        displayName: 'Credential',
        type: 'credential',
        required: true,
        description: 'GitHub API credentials',
      },
      {
        name: 'owner',
        displayName: 'Repository Owner',
        type: 'string',
        required: true,
        placeholder: 'octocat',
        description: 'GitHub username or organization name',
      },
      {
        name: 'repo',
        displayName: 'Repository Name',
        type: 'string',
        required: true,
        placeholder: 'Hello-World',
        description: 'Name of the repository',
      },
      {
        name: 'title',
        displayName: 'Issue Title',
        type: 'string',
        required: true,
        placeholder: 'Bug: Something is broken',
        description: 'Title of the issue',
      },
      {
        name: 'body',
        displayName: 'Issue Body',
        type: 'string',
        required: false,
        placeholder: 'Describe the issue...',
        description: 'Body content of the issue (supports Markdown)',
        rows: 5,
      },
      {
        name: 'labels',
        displayName: 'Labels',
        type: 'string',
        required: false,
        placeholder: 'bug,priority:high',
        description: 'Comma-separated list of labels',
      },
      {
        name: 'assignees',
        displayName: 'Assignees',
        type: 'string',
        required: false,
        placeholder: 'username1,username2',
        description: 'Comma-separated list of usernames to assign',
      },
      {
        name: 'milestone',
        displayName: 'Milestone',
        type: 'number',
        required: false,
        placeholder: '1',
        description: 'Milestone number to associate with the issue',
      },
    ],
    credentials: [
      {
        name: 'githubApi',
        required: true,
      },
    ],
    documentation: {
      description: 'Create issues in GitHub repositories to track bugs, feature requests, or tasks.',
      examples: [
        {
          title: 'Bug Report',
          description: 'Create a bug report issue',
          configuration: {
            owner: 'myorg',
            repo: 'myproject',
            title: 'Bug: Login form validation fails',
            body: '## Description\nThe login form does not validate email addresses properly.\n\n## Steps to Reproduce\n1. Go to login page\n2. Enter invalid email\n3. Click submit\n\n## Expected Behavior\nShould show validation error\n\n## Actual Behavior\nForm submits anyway',
            labels: 'bug,priority:high',
            assignees: 'developer1',
          },
        },
      ],
    },
  },
  {
    name: 'GitHub Get Repository',
    type: 'action',
    category: 'Development',
    description: 'Get information about a GitHub repository',
    version: '1.0.0',
    icon: '📁',
    color: '#24292e',
    tags: ['github', 'repository', 'info'],
    inputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Input',
        required: false,
      },
    ],
    outputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Output',
      },
    ],
    properties: [
      {
        name: 'credential',
        displayName: 'Credential',
        type: 'credential',
        required: true,
        description: 'GitHub API credentials',
      },
      {
        name: 'owner',
        displayName: 'Repository Owner',
        type: 'string',
        required: true,
        placeholder: 'octocat',
        description: 'GitHub username or organization name',
      },
      {
        name: 'repo',
        displayName: 'Repository Name',
        type: 'string',
        required: true,
        placeholder: 'Hello-World',
        description: 'Name of the repository',
      },
    ],
    credentials: [
      {
        name: 'githubApi',
        required: true,
      },
    ],
  },
  {
    name: 'GitHub Create Pull Request',
    type: 'action',
    category: 'Development',
    description: 'Create a new pull request in a GitHub repository',
    version: '1.0.0',
    icon: '🔀',
    color: '#24292e',
    tags: ['github', 'pull-request', 'development'],
    inputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Input',
        required: false,
      },
    ],
    outputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Output',
      },
    ],
    properties: [
      {
        name: 'credential',
        displayName: 'Credential',
        type: 'credential',
        required: true,
        description: 'GitHub API credentials',
      },
      {
        name: 'owner',
        displayName: 'Repository Owner',
        type: 'string',
        required: true,
        placeholder: 'octocat',
        description: 'GitHub username or organization name',
      },
      {
        name: 'repo',
        displayName: 'Repository Name',
        type: 'string',
        required: true,
        placeholder: 'Hello-World',
        description: 'Name of the repository',
      },
      {
        name: 'title',
        displayName: 'Pull Request Title',
        type: 'string',
        required: true,
        placeholder: 'Add new feature',
        description: 'Title of the pull request',
      },
      {
        name: 'head',
        displayName: 'Head Branch',
        type: 'string',
        required: true,
        placeholder: 'feature-branch',
        description: 'Branch containing the changes',
      },
      {
        name: 'base',
        displayName: 'Base Branch',
        type: 'string',
        required: true,
        placeholder: 'main',
        description: 'Branch to merge changes into',
        default: 'main',
      },
      {
        name: 'body',
        displayName: 'Pull Request Body',
        type: 'string',
        required: false,
        placeholder: 'Describe the changes...',
        description: 'Body content of the pull request (supports Markdown)',
        rows: 5,
      },
      {
        name: 'draft',
        displayName: 'Draft',
        type: 'boolean',
        required: false,
        default: false,
        description: 'Create as draft pull request',
      },
    ],
    credentials: [
      {
        name: 'githubApi',
        required: true,
      },
    ],
  },
  {
    name: 'GitHub List Issues',
    type: 'action',
    category: 'Development',
    description: 'List issues from a GitHub repository',
    version: '1.0.0',
    icon: '📋',
    color: '#24292e',
    tags: ['github', 'issues', 'list'],
    inputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Input',
        required: false,
      },
    ],
    outputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Output',
      },
    ],
    properties: [
      {
        name: 'credential',
        displayName: 'Credential',
        type: 'credential',
        required: true,
        description: 'GitHub API credentials',
      },
      {
        name: 'owner',
        displayName: 'Repository Owner',
        type: 'string',
        required: true,
        placeholder: 'octocat',
        description: 'GitHub username or organization name',
      },
      {
        name: 'repo',
        displayName: 'Repository Name',
        type: 'string',
        required: true,
        placeholder: 'Hello-World',
        description: 'Name of the repository',
      },
      {
        name: 'state',
        displayName: 'State',
        type: 'select',
        required: false,
        default: 'open',
        options: [
          { value: 'open', label: 'Open' },
          { value: 'closed', label: 'Closed' },
          { value: 'all', label: 'All' },
        ],
        description: 'Filter issues by state',
      },
      {
        name: 'labels',
        displayName: 'Labels',
        type: 'string',
        required: false,
        placeholder: 'bug,enhancement',
        description: 'Comma-separated list of labels to filter by',
      },
      {
        name: 'sort',
        displayName: 'Sort',
        type: 'select',
        required: false,
        default: 'created',
        options: [
          { value: 'created', label: 'Created' },
          { value: 'updated', label: 'Updated' },
          { value: 'comments', label: 'Comments' },
        ],
        description: 'Sort issues by',
      },
      {
        name: 'direction',
        displayName: 'Direction',
        type: 'select',
        required: false,
        default: 'desc',
        options: [
          { value: 'asc', label: 'Ascending' },
          { value: 'desc', label: 'Descending' },
        ],
        description: 'Sort direction',
      },
      {
        name: 'perPage',
        displayName: 'Per Page',
        type: 'number',
        required: false,
        default: 30,
        min: 1,
        max: 100,
        description: 'Number of issues to return per page',
      },
    ],
    credentials: [
      {
        name: 'githubApi',
        required: true,
      },
    ],
  },
];
