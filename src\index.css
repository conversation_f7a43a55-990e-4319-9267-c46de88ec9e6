@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 184 100% 10%;

    --card: 0 0% 100%;
    --card-foreground: 184 100% 10%;

    --popover: 0 0% 100%;
    --popover-foreground: 184 100% 10%;

    --primary: 184 100% 15%;
    --primary-foreground: 0 0% 100%;

    --secondary: 184 100% 85%;
    --secondary-foreground: 184 100% 10%;

    --muted: 184 100% 85%;
    --muted-foreground: 184 100% 25%;

    --accent: 184 100% 50%;
    --accent-foreground: 0 0% 100%;

    --destructive: 15 60% 40%;
    --destructive-foreground: 0 0% 100%;

    --border: 184 100% 70%;
    --input: 184 100% 70%;
    --ring: 184 100% 50%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 184 100% 10%;
    --foreground: 0 0% 100%;

    --card: 184 100% 10%;
    --card-foreground: 0 0% 100%;

    --popover: 184 100% 10%;
    --popover-foreground: 0 0% 100%;

    --primary: 184 100% 50%;
    --primary-foreground: 184 100% 10%;

    --secondary: 184 100% 15%;
    --secondary-foreground: 0 0% 100%;

    --muted: 184 100% 15%;
    --muted-foreground: 184 100% 70%;

    --accent: 184 100% 25%;
    --accent-foreground: 0 0% 100%;

    --destructive: 15 60% 40%;
    --destructive-foreground: 0 0% 100%;

    --border: 184 100% 15%;
    --input: 184 100% 15%;
    --ring: 184 100% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }

    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(30px);
    }

    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }

    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes float {

    0%,
    100% {
      transform: translateY(0px);
    }

    50% {
      transform: translateY(-10px);
    }
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
}