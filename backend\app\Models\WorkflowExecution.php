<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class WorkflowExecution extends Model
{
    protected $fillable = [
        'workflow_id',
        'trigger_id',
        'user_id',
        'execution_id',
        'status',
        'started_at',
        'finished_at',
        'duration',
        'nodes_executed',
        'total_nodes',
        'input_data',
        'output_data',
        'error_message',
        'error_node_id',
        'metadata',
    ];

    protected $casts = [
        'started_at' => 'datetime',
        'finished_at' => 'datetime',
        'duration' => 'integer',
        'nodes_executed' => 'integer',
        'total_nodes' => 'integer',
        'input_data' => 'array',
        'output_data' => 'array',
        'metadata' => 'array',
    ];

    public function workflow(): BelongsTo
    {
        return $this->belongsTo(Workflow::class);
    }

    public function trigger(): BelongsTo
    {
        return $this->belongsTo(WorkflowTrigger::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function nodeExecutions(): HasMany
    {
        return $this->hasMany(NodeExecution::class);
    }

    // Scope for successful executions
    public function scopeSuccessful($query)
    {
        return $query->where('status', 'success');
    }

    // Scope for failed executions
    public function scopeFailed($query)
    {
        return $query->where('status', 'error');
    }

    // Scope for running executions
    public function scopeRunning($query)
    {
        return $query->where('status', 'running');
    }

    // Scope for recent executions
    public function scopeRecent($query, $days = 7)
    {
        return $query->where('started_at', '>=', now()->subDays($days));
    }

    // Check if execution is successful
    public function isSuccessful(): bool
    {
        return $this->status === 'success';
    }

    // Check if execution failed
    public function isFailed(): bool
    {
        return $this->status === 'error';
    }

    // Check if execution is running
    public function isRunning(): bool
    {
        return $this->status === 'running';
    }

    // Get execution duration in seconds
    public function getDurationInSeconds(): ?float
    {
        return $this->duration ? $this->duration / 1000 : null;
    }

    // Get completion percentage
    public function getCompletionPercentage(): float
    {
        if ($this->total_nodes === 0) {
            return 0;
        }

        return ($this->nodes_executed / $this->total_nodes) * 100;
    }

    // Mark execution as started
    public function markAsStarted(): void
    {
        $this->update([
            'status' => 'running',
            'started_at' => now(),
        ]);
    }

    // Mark execution as completed
    public function markAsCompleted(array $outputData = null): void
    {
        $this->update([
            'status' => 'success',
            'finished_at' => now(),
            'duration' => $this->started_at ? now()->diffInMilliseconds($this->started_at) : 0,
            'output_data' => $outputData,
        ]);
    }

    // Mark execution as failed
    public function markAsFailed(string $errorMessage, string $errorNodeId = null): void
    {
        $this->update([
            'status' => 'error',
            'finished_at' => now(),
            'duration' => $this->started_at ? now()->diffInMilliseconds($this->started_at) : 0,
            'error_message' => $errorMessage,
            'error_node_id' => $errorNodeId,
        ]);
    }

    // Update execution progress
    public function updateProgress(int $nodesExecuted): void
    {
        $this->update([
            'nodes_executed' => $nodesExecuted,
        ]);
    }
}
