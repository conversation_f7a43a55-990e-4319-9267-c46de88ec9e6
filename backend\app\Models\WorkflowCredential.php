<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Crypt;

class WorkflowCredential extends Model
{
    protected $fillable = [
        'workflow_id',
        'user_id',
        'name',
        'type',
        'data',
        'is_active',
        'last_used_at',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'last_used_at' => 'datetime',
    ];

    protected $hidden = [
        'data', // Hide encrypted data from JSON serialization
    ];

    public function workflow(): BelongsTo
    {
        return $this->belongsTo(Workflow::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Scope for active credentials
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // Scope by credential type
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    // Encrypt and store credential data
    public function setDataAttribute($value): void
    {
        $this->attributes['data'] = Crypt::encrypt($value);
    }

    // Decrypt and retrieve credential data
    public function getDataAttribute($value): array
    {
        try {
            return Crypt::decrypt($value);
        } catch (\Exception $e) {
            return [];
        }
    }

    // Get decrypted credential data
    public function getDecryptedData(): array
    {
        return $this->data ?? [];
    }

    // Set credential data (will be encrypted)
    public function setCredentialData(array $data): void
    {
        $this->data = $data;
        $this->save();
    }

    // Get specific credential value
    public function getCredentialValue(string $key, $default = null)
    {
        $data = $this->getDecryptedData();
        return $data[$key] ?? $default;
    }

    // Set specific credential value
    public function setCredentialValue(string $key, $value): void
    {
        $data = $this->getDecryptedData();
        $data[$key] = $value;
        $this->setCredentialData($data);
    }

    // Mark credential as used
    public function markAsUsed(): void
    {
        $this->update(['last_used_at' => now()]);
    }

    // Test credential connection (placeholder)
    public function testConnection(): array
    {
        // This would implement actual credential testing based on type
        // For now, return a mock response
        return [
            'success' => true,
            'message' => 'Credential test not implemented for type: ' . $this->type,
        ];
    }

    // Check if credential is expired (if applicable)
    public function isExpired(): bool
    {
        $data = $this->getDecryptedData();
        
        if (isset($data['expires_at'])) {
            return now()->isAfter($data['expires_at']);
        }

        return false;
    }

    // Get credential summary for display (without sensitive data)
    public function getSummary(): array
    {
        $data = $this->getDecryptedData();
        $summary = [];

        // Include non-sensitive fields based on credential type
        switch ($this->type) {
            case 'http_basic':
                $summary['username'] = $data['username'] ?? null;
                break;
            case 'http_header':
                $summary['header_name'] = $data['header_name'] ?? null;
                break;
            case 'oauth2':
                $summary['client_id'] = $data['client_id'] ?? null;
                $summary['scope'] = $data['scope'] ?? null;
                break;
            case 'database':
                $summary['host'] = $data['host'] ?? null;
                $summary['database'] = $data['database'] ?? null;
                $summary['username'] = $data['username'] ?? null;
                break;
            case 'email':
                $summary['host'] = $data['host'] ?? null;
                $summary['username'] = $data['username'] ?? null;
                break;
        }

        return $summary;
    }
}
