<?php

namespace App\Services;

use Ratchet\MessageComponentInterface;
use Ratchet\ConnectionInterface;
use Ratchet\WebSocket\WsServer;
use Ratchet\Http\HttpServer;
use Ratchet\Server\IoServer;
use Illuminate\Support\Facades\Log;

class ExecutionWebSocketServer implements MessageComponentInterface
{
    protected $clients;
    protected $subscriptions;

    public function __construct()
    {
        $this->clients = new \SplObjectStorage;
        $this->subscriptions = [];
    }

    public function onOpen(ConnectionInterface $conn)
    {
        $this->clients->attach($conn);
        Log::info("New WebSocket connection: {$conn->resourceId}");
    }

    public function onMessage(ConnectionInterface $from, $msg)
    {
        try {
            $data = json_decode($msg, true);
            
            if (!$data || !isset($data['type'])) {
                $this->sendError($from, 'Invalid message format');
                return;
            }

            switch ($data['type']) {
                case 'subscribe':
                    $this->handleSubscribe($from, $data);
                    break;
                case 'unsubscribe':
                    $this->handleUnsubscribe($from, $data);
                    break;
                default:
                    $this->sendError($from, 'Unknown message type');
            }
        } catch (\Exception $e) {
            Log::error('WebSocket message error: ' . $e->getMessage());
            $this->sendError($from, 'Message processing failed');
        }
    }

    public function onClose(ConnectionInterface $conn)
    {
        $this->clients->detach($conn);
        $this->removeSubscriptions($conn);
        Log::info("Connection closed: {$conn->resourceId}");
    }

    public function onError(ConnectionInterface $conn, \Exception $e)
    {
        Log::error("WebSocket error on connection {$conn->resourceId}: " . $e->getMessage());
        $conn->close();
    }

    protected function handleSubscribe(ConnectionInterface $conn, array $data)
    {
        $executionId = $data['executionId'] ?? null;
        $workflowId = $data['workflowId'] ?? null;

        if ($executionId) {
            $this->addSubscription($conn, 'execution', $executionId);
            Log::info("Client {$conn->resourceId} subscribed to execution {$executionId}");
        }

        if ($workflowId) {
            $this->addSubscription($conn, 'workflow', $workflowId);
            Log::info("Client {$conn->resourceId} subscribed to workflow {$workflowId}");
        }
    }

    protected function handleUnsubscribe(ConnectionInterface $conn, array $data)
    {
        $executionId = $data['executionId'] ?? null;
        $workflowId = $data['workflowId'] ?? null;

        if ($executionId) {
            $this->removeSubscription($conn, 'execution', $executionId);
            Log::info("Client {$conn->resourceId} unsubscribed from execution {$executionId}");
        }

        if ($workflowId) {
            $this->removeSubscription($conn, 'workflow', $workflowId);
            Log::info("Client {$conn->resourceId} unsubscribed from workflow {$workflowId}");
        }
    }

    protected function addSubscription(ConnectionInterface $conn, string $type, string $id)
    {
        $key = "{$type}:{$id}";
        
        if (!isset($this->subscriptions[$key])) {
            $this->subscriptions[$key] = new \SplObjectStorage;
        }
        
        $this->subscriptions[$key]->attach($conn);
    }

    protected function removeSubscription(ConnectionInterface $conn, string $type, string $id)
    {
        $key = "{$type}:{$id}";
        
        if (isset($this->subscriptions[$key])) {
            $this->subscriptions[$key]->detach($conn);
            
            if ($this->subscriptions[$key]->count() === 0) {
                unset($this->subscriptions[$key]);
            }
        }
    }

    protected function removeSubscriptions(ConnectionInterface $conn)
    {
        foreach ($this->subscriptions as $key => $storage) {
            $storage->detach($conn);
            
            if ($storage->count() === 0) {
                unset($this->subscriptions[$key]);
            }
        }
    }

    protected function sendError(ConnectionInterface $conn, string $message)
    {
        $conn->send(json_encode([
            'type' => 'error',
            'error' => $message,
        ]));
    }

    public function broadcastExecutionEvent(string $executionId, string $workflowId, array $event)
    {
        $message = json_encode([
            'type' => 'execution_event',
            'event' => $event,
        ]);

        // Broadcast to execution subscribers
        $executionKey = "execution:{$executionId}";
        if (isset($this->subscriptions[$executionKey])) {
            foreach ($this->subscriptions[$executionKey] as $conn) {
                try {
                    $conn->send($message);
                } catch (\Exception $e) {
                    Log::error("Failed to send message to client: " . $e->getMessage());
                }
            }
        }

        // Broadcast to workflow subscribers
        $workflowKey = "workflow:{$workflowId}";
        if (isset($this->subscriptions[$workflowKey])) {
            foreach ($this->subscriptions[$workflowKey] as $conn) {
                try {
                    $conn->send($message);
                } catch (\Exception $e) {
                    Log::error("Failed to send message to client: " . $e->getMessage());
                }
            }
        }

        Log::info("Broadcasted execution event to subscribers", [
            'executionId' => $executionId,
            'workflowId' => $workflowId,
            'eventType' => $event['type'] ?? 'unknown',
        ]);
    }

    public static function start(int $port = 8080)
    {
        $server = new ExecutionWebSocketServer();
        
        $wsServer = new WsServer($server);
        $httpServer = new HttpServer($wsServer);
        $ioServer = IoServer::factory($httpServer, $port);

        Log::info("WebSocket server starting on port {$port}");
        
        $ioServer->run();
    }

    public function getSubscriptionCount(): int
    {
        return count($this->subscriptions);
    }

    public function getClientCount(): int
    {
        return $this->clients->count();
    }
}
