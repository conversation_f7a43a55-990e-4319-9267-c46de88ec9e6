import React, { useState, use<PERSON>allback, useRef, useEffect, useMemo } from "react";

// Add custom CSS for animations
const customStyles = `
  @keyframes fadeIn {
    from { opacity: 0; transform: scale(0.8); }
    to { opacity: 1; transform: scale(1); }
  }

  @keyframes slideDown {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  @keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
  }

  @keyframes connectionFlow {
    0% { stroke-dashoffset: 20; }
    100% { stroke-dashoffset: 0; }
  }

  .animate-fadeIn {
    animation: fadeIn 0.2s ease-out;
  }

  .animate-slideDown {
    animation: slideDown 0.2s ease-out;
  }

  .animate-connectionFlow {
    animation: connectionFlow 2s linear infinite;
  }

  .connection-highlight {
    filter: drop-shadow(0 0 8px rgba(150, 71, 52, 0.6));
  }

  .node-connecting {
    transform: scale(1.05);
    filter: drop-shadow(0 0 12px rgba(15, 164, 175, 0.8));
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement("style");
  styleSheet.innerText = customStyles;
  document.head.appendChild(styleSheet);
}
import ReactFlow, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  MiniMap,
  ReactFlowProvider,
  ReactFlowInstance,
  NodeTypes,
  MarkerType,
  EdgeTypes,
} from "reactflow";
import "reactflow/dist/style.css";
import { apiService, type WorkflowTemplate as ApiWorkflowTemplate, type Workflow as ApiWorkflow } from "@/lib/api";
import { nodeRegistry } from '@/lib/nodes/registry';
import { integrationRegistry } from '@/lib/nodes/integrations';
import { NodeBrowser } from './NodeBrowser';
import { ExecutionMonitor } from './ExecutionMonitor';

import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import AnimatedDemoTrigger from "@/components/tutorial/AnimatedDemoTrigger";
import {
  Play,
  Save,
  ArrowRight,
  MessageSquare,
  Brain,
  Bot,
  Database,
  Zap,
  Shield,
  GitBranch,
  Clock,
  Mail,
  BarChart3,
  Users,
  FileText,
  Workflow,
  Plus,
  ArrowLeft,
  Settings,
  ZoomIn,
  ZoomOut,
  Trash2,
  Copy,
  Edit,
  Globe,
  Webhook,
  Calendar,
  MousePointer,
  Filter,
  Repeat,
  Timer,
  Send,
  CheckCircle,
  AlertCircle,
  X,
  Link,
} from "lucide-react";

// Node type definitions for drag and drop
interface NodeData {
  label: string;
  description?: string;
  icon?: React.ReactNode;
  category: string;
  config?: Record<string, any>;
  isConfigured?: boolean;
  status?: 'idle' | 'running' | 'success' | 'error';
  isSuggested?: boolean;
  isSourceNode?: boolean;
}

interface DraggableNodeType {
  id: string;
  type: "trigger" | "action" | "logic" | "output";
  label: string;
  description: string;
  icon: React.ReactNode;
  category: string;
  color: string;
  bgColor: string;
  borderColor: string;
  defaultConfig?: Record<string, any>;
}

interface FlowNode {
  id: string;
  type: "start" | "process" | "decision" | "end";
  title: string;
  description: string;
  icon: React.ReactNode;
  category: string;
  connections: string[];
}

interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  estimatedTime: string;
  complexity: "Simple" | "Medium" | "Advanced";
  nodes: FlowNode[];
}

interface WorkflowState {
  name: string;
  description: string;
  isActive: boolean;
  nodes: Node[];
  edges: Edge[];
}

// Custom Edge Component with Toggle and Flow Indicators
const CustomEdge = ({ id, sourceX, sourceY, targetX, targetY, style, markerEnd, data }: any) => {
  const [isActive, setIsActive] = useState(data?.isActive || false);
  const [showToggle, setShowToggle] = useState(false);

  const midX = (sourceX + targetX) / 2;
  const midY = (sourceY + targetY) / 2;

  const toggleConnection = () => {
    setIsActive(!isActive);
  };

  const edgeStyle = {
    ...style,
    stroke: isActive ? '#0FA4AF' : '#964734',
    strokeWidth: isActive ? 4 : 2,
    strokeDasharray: isActive ? 'none' : '5,5'
  };

  const flowIndicators = [];
  const numIndicators = 3;
  for (let i = 1; i <= numIndicators; i++) {
    const t = i / (numIndicators + 1);
    const x = sourceX + (targetX - sourceX) * t;
    const y = sourceY + (targetY - sourceY) * t;

    flowIndicators.push(
      <circle
        key={`flow-${i}`}
        cx={x}
        cy={y}
        r="3"
        fill={isActive ? '#0FA4AF' : '#964734'}
        opacity={0.8}
        className={isActive ? 'animate-pulse' : ''}
      />
    );
  }

  return (
    <g>
      {/* Main connection line */}
      <path
        d={`M ${sourceX} ${sourceY} Q ${midX} ${sourceY} ${targetX} ${targetY}`}
        style={edgeStyle}
        fill="none"
        markerEnd={markerEnd}
        onMouseEnter={() => setShowToggle(true)}
        onMouseLeave={() => setShowToggle(false)}
        className="cursor-pointer"
      />

      {/* Flow direction indicators */}
      {flowIndicators}

      {/* Connection toggle button */}
      {showToggle && (
        <g>
          <circle
            cx={midX}
            cy={midY}
            r="12"
            fill="white"
            stroke={isActive ? '#0FA4AF' : '#964734'}
            strokeWidth="2"
            className="cursor-pointer drop-shadow-lg"
            onClick={toggleConnection}
          />
          <text
            x={midX}
            y={midY + 1}
            textAnchor="middle"
            fontSize="10"
            fill={isActive ? '#0FA4AF' : '#964734'}
            className="cursor-pointer select-none"
            onClick={toggleConnection}
          >
            {isActive ? '✓' : '○'}
          </text>
        </g>
      )}

      {/* Connection status indicator */}
      <circle
        cx={midX}
        cy={midY - 20}
        r="4"
        fill={isActive ? '#0FA4AF' : '#964734'}
        opacity={0.6}
      />
    </g>
  );
};

// WORKING Custom Node with Real Drag Connections
const CustomNode = ({
  data,
  selected,
  id
}: {
  data: NodeData;
  selected: boolean;
  id: string;
}) => {
  const [showHandles, setShowHandles] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [hoverTimeout, setHoverTimeout] = useState<NodeJS.Timeout | null>(null);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (hoverTimeout) clearTimeout(hoverTimeout);
    };
  }, [hoverTimeout]);

  // WORKING drag connection system
  const handleMouseDown = (event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();
    setIsDragging(true);

    // Start drag connection
    const customEvent = new CustomEvent('startConnection', {
      detail: {
        sourceNodeId: id,
        startX: event.clientX,
        startY: event.clientY
      }
    });
    window.dispatchEvent(customEvent);
  };

  const handleMouseUp = (event: React.MouseEvent) => {
    if (isDragging) {
      event.preventDefault();
      event.stopPropagation();
      setIsDragging(false);

      // Complete connection
      const customEvent = new CustomEvent('endConnection', {
        detail: {
          targetNodeId: id,
          endX: event.clientX,
          endY: event.clientY
        }
      });
      window.dispatchEvent(customEvent);
    }
  };

  // Check if this node is suggested for connection (will be passed as props)
  const isSuggested = data.isSuggested || false;
  const isSourceNode = data.isSourceNode || false;

  return (
    <div
      className={`relative px-4 py-3 shadow-lg rounded-xl border-2 bg-white dark:bg-[#003135] min-w-[160px] transition-all duration-300 cursor-pointer group ${selected
        ? "border-[#964734] shadow-[#964734]/30 scale-105"
        : isSourceNode
          ? "border-[#0FA4AF] shadow-[#0FA4AF]/50 scale-105 node-connecting"
          : isSuggested
            ? "border-green-500 shadow-green-500/50 animate-pulse connection-highlight"
            : data.status === 'running'
              ? "border-[#0FA4AF] shadow-[#0FA4AF]/30 animate-pulse"
              : data.status === 'success'
                ? "border-green-500 shadow-green-500/30"
                : data.status === 'error'
                  ? "border-red-500 shadow-red-500/30"
                  : "border-[#0FA4AF]/30 hover:border-[#964734]/50 hover:shadow-xl"
        }`}
      onMouseEnter={() => {
        if (hoverTimeout) clearTimeout(hoverTimeout);
        setShowHandles(true);
      }}
      onMouseLeave={() => {
        // Delay hiding to allow clicking plus button
        const timeout = setTimeout(() => setShowHandles(false), 300);
        setHoverTimeout(timeout);
      }}
    >
      {/* Input Handle - Drop Zone */}
      {data.category !== 'trigger' && (
        <div
          className={`absolute -left-3 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-[#964734] border-2 border-white rounded-full flex items-center justify-center cursor-pointer transition-all duration-200 hover:scale-125 ${showHandles ? 'opacity-100 scale-110' : 'opacity-60 scale-90'
            }`}
          title="Drop connection here"
          onMouseUp={handleMouseUp}
        >
          <div className="w-3 h-3 bg-white rounded-full"></div>
        </div>
      )}

      {/* Output Handle - Drag Source */}
      {data.category !== 'output' && (
        <div
          className={`absolute -right-3 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-[#0FA4AF] border-2 border-white rounded-full flex items-center justify-center cursor-grab active:cursor-grabbing transition-all duration-200 hover:scale-125 ${showHandles ? 'opacity-100 scale-110' : 'opacity-60 scale-90'
            } ${isDragging ? 'scale-125 ring-2 ring-[#0FA4AF]' : ''}`}
          title="Drag to connect"
          onMouseDown={handleMouseDown}
          draggable={false}
        >
          <ArrowRight className="w-4 h-4 text-white" />
        </div>
      )}

      {/* Auto-Connect Plus Button */}
      {data.category !== 'output' && showHandles && (
        <div
          className="absolute -right-12 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-gradient-to-r from-[#964734] to-[#0FA4AF] border-2 border-white rounded-full flex items-center justify-center cursor-pointer transition-all duration-200 hover:scale-125 hover:shadow-lg animate-fadeIn shadow-md"
          title="🚀 Auto-connect next step"
          onMouseEnter={() => {
            if (hoverTimeout) clearTimeout(hoverTimeout);
            setShowHandles(true);
          }}
          onMouseLeave={() => {
            const timeout = setTimeout(() => setShowHandles(false), 300);
            setHoverTimeout(timeout);
          }}
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            // Keep button visible after click
            if (hoverTimeout) clearTimeout(hoverTimeout);
            const customEvent = new CustomEvent('autoConnect', {
              detail: { nodeId: id }
            });
            window.dispatchEvent(customEvent);
          }}
        >
          <Plus className="w-4 h-4 text-white font-bold" />
        </div>
      )}

      {/* Auto-Connect Hint - Positioned above button */}
      {data.category !== 'output' && showHandles && (
        <div className="absolute -right-16 -top-8 bg-[#964734] text-white text-xs px-2 py-1 rounded shadow-lg animate-fadeIn whitespace-nowrap">
          Click + for suggestions
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-[#964734]"></div>
        </div>
      )}

      {/* Node Content */}
      <div className="flex items-center space-x-3">
        <div className={`p-2 rounded-lg ${data.category === "trigger" ? "bg-[#0FA4AF]/20" :
          data.category === "action" ? "bg-[#024950]/20" :
            data.category === "logic" ? "bg-[#964734]/20" : "bg-[#AFDDE5]/20"}`}>
          {data.icon}
        </div>
        <div className="flex-1">
          <div className="font-semibold text-sm text-[#003135] dark:text-white">
            {data.label}
          </div>
          {data.description && (
            <div className="text-xs text-[#024950] dark:text-[#AFDDE5] mt-1">
              {data.description}
            </div>
          )}
        </div>
        <div className="flex items-center space-x-1">
          {data.status === 'running' && <div className="w-2 h-2 bg-[#0FA4AF] rounded-full animate-pulse" />}
          {data.status === 'success' && <CheckCircle className="h-4 w-4 text-green-500" />}
          {data.status === 'error' && <AlertCircle className="h-4 w-4 text-red-500" />}
          {data.isConfigured && !data.status && (
            <CheckCircle className="h-4 w-4 text-[#0FA4AF]" />
          )}
        </div>
      </div>

      {/* Validation Indicator */}
      {data.category === 'trigger' && (
        <div className="absolute -top-2 -left-2 w-4 h-4 bg-[#0FA4AF] rounded-full flex items-center justify-center">
          <span className="text-xs text-white font-bold">S</span>
        </div>
      )}

      {data.category === 'output' && (
        <div className="absolute -top-2 -right-2 w-4 h-4 bg-[#964734] rounded-full flex items-center justify-center">
          <span className="text-xs text-white font-bold">E</span>
        </div>
      )}
    </div>
  );
};

// Draggable Node Component
const DraggableNode = ({ nodeType, onDragStart }: {
  nodeType: DraggableNodeType;
  onDragStart: (nodeType: DraggableNodeType) => void;
}) => {
  const handleDragStart = (event: React.DragEvent) => {
    // Create a serializable version without the icon
    const serializableNodeType = {
      ...nodeType,
      icon: undefined, // Remove the React component
    };
    event.dataTransfer.setData("application/reactflow", JSON.stringify(serializableNodeType));
    event.dataTransfer.effectAllowed = "move";
    onDragStart(nodeType);
  };

  return (
    <div
      draggable
      onDragStart={handleDragStart}
      className={`draggable-node p-3 border rounded-xl cursor-grab hover:cursor-grabbing transition-all duration-300 hover:shadow-lg hover:scale-105 ${nodeType.borderColor} ${nodeType.bgColor} group`}
    >
      <div className="flex items-center space-x-3">
        <div className={`p-2 rounded-lg ${nodeType.color} group-hover:scale-110 transition-transform duration-300`}>
          {nodeType.icon}
        </div>
        <div className="flex-1">
          <div className="font-medium text-sm text-[#003135] dark:text-white">
            {nodeType.label}
          </div>
          <div className="text-xs text-[#024950] dark:text-[#AFDDE5] mt-1">
            {nodeType.description}
          </div>
        </div>
      </div>
    </div>
  );
};

const WorkflowBuilder = () => {
  const [currentView, setCurrentView] = useState<"templates" | "preview" | "builder">("templates");
  const [selectedTemplate, setSelectedTemplate] = useState<WorkflowTemplate | null>(null);
  const [apiTemplates, setApiTemplates] = useState<ApiWorkflowTemplate[]>([]);
  const [currentWorkflow, setCurrentWorkflow] = useState<ApiWorkflow | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Workflow state
  const [workflow, setWorkflow] = useState<WorkflowState>({
    name: "New Workflow",
    description: "",
    isActive: false,
    nodes: [],
    edges: [],
  });

  // React Flow state
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [draggedNodeType, setDraggedNodeType] = useState<DraggableNodeType | null>(null);
  const [isConfigDialogOpen, setIsConfigDialogOpen] = useState(false);
  const [connectionMode, setConnectionMode] = useState<'off' | 'selecting' | 'connecting' | 'suggesting'>('off');
  const [sourceNode, setSourceNode] = useState<string | null>(null);
  const [showConnectionHelper, setShowConnectionHelper] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [showNodeBrowser, setShowNodeBrowser] = useState(false);
  const [showExecutionMonitor, setShowExecutionMonitor] = useState(false);
  const [currentExecution, setCurrentExecution] = useState<{
    executionId: string;
    workflowId: string;
    workflowName: string;
  } | null>(null);
  const [dragStart, setDragStart] = useState<{ x: number; y: number; nodeId: string } | null>(null);
  const [dragCurrent, setDragCurrent] = useState<{ x: number; y: number } | null>(null);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [suggestedConnections, setSuggestedConnections] = useState<string[]>([]);
  const [connectionMethod, setConnectionMethod] = useState<'quick' | 'drag' | 'plus' | 'auto'>('quick');
  const [previewConnection, setPreviewConnection] = useState<{ from: string; to: { x: number; y: number } } | null>(null);
  const [mousePosition, setMousePosition] = useState<{ x: number; y: number }>({ x: 0, y: 0 });
  const [autoSuggestions, setAutoSuggestions] = useState<Array<{ id: string, label: string, category: string, confidence: number }>>([]);
  const [showAutoConnect, setShowAutoConnect] = useState(false);

  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const [reactFlowInstance, setReactFlowInstance] = useState<ReactFlowInstance | null>(null);

  // Load templates from API on component mount
  useEffect(() => {
    const loadTemplates = async () => {
      try {
        setIsLoading(true);
        const response = await apiService.getWorkflowTemplates();
        setApiTemplates(response.data);
      } catch (error) {
        console.error('Failed to load templates:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadTemplates();
  }, []);



  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  // Available node types for dragging
  const nodeTypes: DraggableNodeType[] = [
    // Trigger nodes
    {
      id: "webhook",
      type: "trigger",
      label: "Webhook",
      description: "HTTP webhook trigger",
      icon: <Webhook className="h-4 w-4 text-white" />,
      category: "trigger",
      color: "bg-[#0FA4AF]",
      bgColor: "bg-[#0FA4AF]/10 hover:bg-[#0FA4AF]/20",
      borderColor: "border-[#0FA4AF]/30 hover:border-[#0FA4AF]",
      defaultConfig: { method: "POST", path: "/webhook" },
    },
    {
      id: "schedule",
      type: "trigger",
      label: "Schedule",
      description: "Time-based trigger",
      icon: <Calendar className="h-4 w-4 text-white" />,
      category: "trigger",
      color: "bg-[#0FA4AF]",
      bgColor: "bg-[#0FA4AF]/10 hover:bg-[#0FA4AF]/20",
      borderColor: "border-[#0FA4AF]/30 hover:border-[#0FA4AF]",
      defaultConfig: { interval: "daily", time: "09:00" },
    },
    {
      id: "manual",
      type: "trigger",
      label: "Manual",
      description: "Manual trigger",
      icon: <MousePointer className="h-4 w-4 text-white" />,
      category: "trigger",
      color: "bg-[#0FA4AF]",
      bgColor: "bg-[#0FA4AF]/10 hover:bg-[#0FA4AF]/20",
      borderColor: "border-[#0FA4AF]/30 hover:border-[#0FA4AF]",
      defaultConfig: { requireConfirmation: true },
    },
    {
      id: "file-upload",
      type: "trigger",
      label: "File Upload",
      description: "Trigger on file upload",
      icon: <FileText className="h-4 w-4 text-white" />,
      category: "trigger",
      color: "bg-[#0FA4AF]",
      bgColor: "bg-[#0FA4AF]/10 hover:bg-[#0FA4AF]/20",
      borderColor: "border-[#0FA4AF]/30 hover:border-[#0FA4AF]",
      defaultConfig: { allowedTypes: ["pdf", "docx", "txt"], maxSize: "10MB" },
    },
    // Action nodes
    {
      id: "ai-chat",
      type: "action",
      label: "AI Chat",
      description: "Process with AI",
      icon: <Bot className="h-4 w-4 text-white" />,
      category: "action",
      color: "bg-[#024950]",
      bgColor: "bg-[#024950]/10 hover:bg-[#024950]/20",
      borderColor: "border-[#024950]/30 hover:border-[#024950]",
      defaultConfig: { model: "gpt-4", temperature: 0.7 },
    },
    {
      id: "api-call",
      type: "action",
      label: "API Call",
      description: "Make HTTP request",
      icon: <Globe className="h-4 w-4 text-white" />,
      category: "action",
      color: "bg-[#024950]",
      bgColor: "bg-[#024950]/10 hover:bg-[#024950]/20",
      borderColor: "border-[#024950]/30 hover:border-[#024950]",
      defaultConfig: { method: "GET", timeout: 30 },
    },
    {
      id: "send-email",
      type: "action",
      label: "Send Email",
      description: "Send email notification",
      icon: <Mail className="h-4 w-4 text-white" />,
      category: "action",
      color: "bg-[#024950]",
      bgColor: "bg-[#024950]/10 hover:bg-[#024950]/20",
      borderColor: "border-[#024950]/30 hover:border-[#024950]",
      defaultConfig: { provider: "smtp", template: "default" },
    },
    {
      id: "database",
      type: "action",
      label: "Database",
      description: "Database operation",
      icon: <Database className="h-4 w-4 text-white" />,
      category: "action",
      color: "bg-[#024950]",
      bgColor: "bg-[#024950]/10 hover:bg-[#024950]/20",
      borderColor: "border-[#024950]/30 hover:border-[#024950]",
      defaultConfig: { operation: "select", table: "" },
    },
    // Logic nodes
    {
      id: "condition",
      type: "logic",
      label: "Condition",
      description: "Conditional branching",
      icon: <GitBranch className="h-4 w-4 text-white" />,
      category: "logic",
      color: "bg-[#964734]",
      bgColor: "bg-[#964734]/10 hover:bg-[#964734]/20",
      borderColor: "border-[#964734]/30 hover:border-[#964734]",
      defaultConfig: { operator: "equals", value: "" },
    },
    {
      id: "filter",
      type: "logic",
      label: "Filter",
      description: "Filter data",
      icon: <Filter className="h-4 w-4 text-white" />,
      category: "logic",
      color: "bg-[#964734]",
      bgColor: "bg-[#964734]/10 hover:bg-[#964734]/20",
      borderColor: "border-[#964734]/30 hover:border-[#964734]",
      defaultConfig: { field: "", condition: "contains" },
    },
    {
      id: "loop",
      type: "logic",
      label: "Loop",
      description: "Iterate over data",
      icon: <Repeat className="h-4 w-4 text-white" />,
      category: "logic",
      color: "bg-[#964734]",
      bgColor: "bg-[#964734]/10 hover:bg-[#964734]/20",
      borderColor: "border-[#964734]/30 hover:border-[#964734]",
      defaultConfig: { maxIterations: 100 },
    },
    {
      id: "delay",
      type: "logic",
      label: "Delay",
      description: "Wait for specified time",
      icon: <Timer className="h-4 w-4 text-white" />,
      category: "logic",
      color: "bg-[#964734]",
      bgColor: "bg-[#964734]/10 hover:bg-[#964734]/20",
      borderColor: "border-[#964734]/30 hover:border-[#964734]",
      defaultConfig: { duration: 5, unit: "seconds" },
    },
    // Output nodes
    {
      id: "save-result",
      type: "output",
      label: "Save Result",
      description: "Save workflow result",
      icon: <Save className="h-4 w-4 text-white" />,
      category: "output",
      color: "bg-[#AFDDE5]",
      bgColor: "bg-[#AFDDE5]/10 hover:bg-[#AFDDE5]/20",
      borderColor: "border-[#AFDDE5]/30 hover:border-[#AFDDE5]",
      defaultConfig: { format: "json", destination: "database" },
    },
    {
      id: "webhook-response",
      type: "output",
      label: "Webhook Response",
      description: "Send response to webhook",
      icon: <Send className="h-4 w-4 text-white" />,
      category: "output",
      color: "bg-[#AFDDE5]",
      bgColor: "bg-[#AFDDE5]/10 hover:bg-[#AFDDE5]/20",
      borderColor: "border-[#AFDDE5]/30 hover:border-[#AFDDE5]",
      defaultConfig: { statusCode: 200, contentType: "application/json" },
    },
  ];

  // Enhanced nodes with connection state
  const enhancedNodes = useMemo(() =>
    nodes.map(node => ({
      ...node,
      data: {
        ...node.data,
        isSuggested: suggestedConnections.includes(node.id),
        isSourceNode: sourceNode === node.id
      }
    })), [nodes, suggestedConnections, sourceNode]);

  // Memoized custom node and edge types for React Flow
  const customNodeTypes = useMemo(() => ({
    custom: CustomNode,
  }), []);

  const customEdgeTypes = useMemo(() => ({
    custom: CustomEdge,
  }), []);

  // Drag and drop handlers
  const onDragStart = (nodeType: DraggableNodeType) => {
    setDraggedNodeType(nodeType);
  };

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = "move";
  }, []);

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect();
      const type = event.dataTransfer.getData("application/reactflow");

      if (typeof type === "undefined" || !type || !reactFlowInstance || !reactFlowBounds) {
        return;
      }

      const nodeData = JSON.parse(type) as Omit<DraggableNodeType, 'icon'>;
      const position = reactFlowInstance.project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      });

      // Find the original node type to get the icon
      const originalNodeType = nodeTypes.find(nt => nt.id === nodeData.id);

      const newNode: Node = {
        id: `${nodeData.id}-${Date.now()}`,
        type: "custom",
        position,
        data: {
          label: nodeData.label,
          description: nodeData.description,
          icon: originalNodeType?.icon,
          category: nodeData.category,
          config: nodeData.defaultConfig || {},
          isConfigured: false,
        },
      };

      setNodes((nds) => nds.concat(newNode));
      setDraggedNodeType(null);
    },
    [reactFlowInstance, setNodes, nodeTypes]
  );

  // Advanced connection validation and flow logic
  const validateConnection = useCallback((sourceId: string, targetId: string) => {
    const sourceNode = nodes.find(n => n.id === sourceId);
    const targetNode = nodes.find(n => n.id === targetId);

    if (!sourceNode || !targetNode) return { valid: false, error: "Invalid nodes" };

    const sourceCategory = sourceNode.data.category;
    const targetCategory = targetNode.data.category;

    // Validation rules for logical flow
    const validConnections = {
      trigger: ['action', 'logic'],
      action: ['action', 'logic', 'output'],
      logic: ['action', 'logic', 'output'],
      output: []
    };

    if (!validConnections[sourceCategory]?.includes(targetCategory)) {
      return {
        valid: false,
        error: `Cannot connect ${sourceCategory} to ${targetCategory}. ${sourceCategory} nodes can only connect to: ${validConnections[sourceCategory]?.join(', ') || 'nothing'}.`
      };
    }

    // Check for circular dependencies
    const wouldCreateCycle = (source: string, target: string): boolean => {
      const visited = new Set<string>();
      const stack = [target];

      while (stack.length > 0) {
        const current = stack.pop()!;
        if (current === source) return true;
        if (visited.has(current)) continue;
        visited.add(current);

        const outgoingEdges = edges.filter(e => e.source === current);
        stack.push(...outgoingEdges.map(e => e.target));
      }
      return false;
    };

    if (wouldCreateCycle(sourceId, targetId)) {
      return { valid: false, error: "This connection would create a circular dependency" };
    }

    return { valid: true, error: null };
  }, [nodes, edges]);

  const getNodeConnectionPoints = useCallback((nodeId: string) => {
    const nodeElement = document.querySelector(`[data-id="${nodeId}"]`);
    if (!nodeElement) return null;

    const rect = nodeElement.getBoundingClientRect();
    const canvasRect = reactFlowWrapper.current?.getBoundingClientRect();
    if (!canvasRect) return null;

    return {
      input: {
        x: rect.left - canvasRect.left,
        y: rect.top - canvasRect.top + rect.height / 2
      },
      output: {
        x: rect.right - canvasRect.left,
        y: rect.top - canvasRect.top + rect.height / 2
      },
      center: {
        x: rect.left - canvasRect.left + rect.width / 2,
        y: rect.top - canvasRect.top + rect.height / 2
      }
    };
  }, []);

  // Advanced drag-and-connect functionality
  const startConnectionMode = useCallback(() => {
    setConnectionMode('selecting');
    setShowConnectionHelper(true);
    setSourceNode(null);
    setValidationErrors([]);
  }, []);

  const cancelConnectionMode = useCallback(() => {
    setConnectionMode('off');
    setShowConnectionHelper(false);
    setSourceNode(null);
    setDragStart(null);
    setDragCurrent(null);
    setIsDragging(false);
    setValidationErrors([]);
    setSuggestedConnections([]);
    setPreviewConnection(null);
  }, []);

  // Get node position for visual connections
  const getNodePosition = useCallback((nodeId: string) => {
    const node = nodes.find(n => n.id === nodeId);
    if (!node) return null;

    return {
      x: node.position.x + 80, // Center of node (assuming 160px width)
      y: node.position.y + 30  // Center of node (assuming 60px height)
    };
  }, [nodes]);

  // Update mouse position for preview connection
  const updateMousePosition = useCallback((event: React.MouseEvent) => {
    if (connectionMode === 'selecting' && sourceNode) {
      const rect = reactFlowWrapper.current?.getBoundingClientRect();
      if (rect && reactFlowInstance) {
        const position = reactFlowInstance.screenToFlowPosition({
          x: event.clientX - rect.left,
          y: event.clientY - rect.top,
        });

        const sourcePos = getNodePosition(sourceNode);
        if (sourcePos) {
          setPreviewConnection({
            from: sourceNode,
            to: position
          });
        }
      }
    }
  }, [connectionMode, sourceNode, reactFlowInstance, getNodePosition]);

  // Intelligent Auto-Suggestion System
  const getSmartSuggestions = useCallback((nodeId: string) => {
    const sourceNodeData = nodes.find(n => n.id === nodeId);
    if (!sourceNodeData) return [];

    const compatibleNodes = nodes.filter(n => {
      if (n.id === nodeId) return false;
      const validation = validateConnection(nodeId, n.id);
      return validation.valid;
    });

    // Sort by proximity and logical flow
    return compatibleNodes
      .map(node => ({
        ...node,
        distance: Math.sqrt(
          Math.pow(node.position.x - sourceNodeData.position.x, 2) +
          Math.pow(node.position.y - sourceNodeData.position.y, 2)
        )
      }))
      .sort((a, b) => a.distance - b.distance)
      .slice(0, 3) // Top 3 suggestions
      .map(node => node.id);
  }, [nodes, validateConnection]);

  // Enhanced Auto-Connect Intelligence with Better Suggestions
  const getAutoConnectSuggestions = useCallback((sourceNodeId: string) => {
    const sourceNode = nodes.find(n => n.id === sourceNodeId);
    if (!sourceNode) return [];

    const category = sourceNode.data.category;
    const nodeLabel = sourceNode.data.label.toLowerCase();
    const suggestions = [];

    // Context-aware suggestions based on node type and label
    if (category === 'trigger') {
      if (nodeLabel.includes('webhook') || nodeLabel.includes('api')) {
        suggestions.push(
          { id: 'ai-chat', label: 'AI Chat Assistant', category: 'action', confidence: 95 },
          { id: 'data-validation', label: 'Validate Data', category: 'logic', confidence: 90 },
          { id: 'webhook-response', label: 'Send Response', category: 'action', confidence: 85 }
        );
      } else if (nodeLabel.includes('form') || nodeLabel.includes('email')) {
        suggestions.push(
          { id: 'send-confirmation', label: 'Send Confirmation', category: 'action', confidence: 95 },
          { id: 'save-to-database', label: 'Save to Database', category: 'action', confidence: 90 },
          { id: 'notify-team', label: 'Notify Team', category: 'action', confidence: 85 }
        );
      } else {
        suggestions.push(
          { id: 'ai-chat', label: 'AI Chat Assistant', category: 'action', confidence: 95 },
          { id: 'process-data', label: 'Process Data', category: 'action', confidence: 90 },
          { id: 'conditional-check', label: 'Conditional Check', category: 'logic', confidence: 85 }
        );
      }
    } else if (category === 'action') {
      if (nodeLabel.includes('ai') || nodeLabel.includes('chat')) {
        suggestions.push(
          { id: 'save-conversation', label: 'Save Conversation', category: 'action', confidence: 95 },
          { id: 'send-response', label: 'Send Response', category: 'action', confidence: 90 },
          { id: 'escalate-human', label: 'Escalate to Human', category: 'logic', confidence: 85 }
        );
      } else if (nodeLabel.includes('database') || nodeLabel.includes('save')) {
        suggestions.push(
          { id: 'send-notification', label: 'Send Notification', category: 'action', confidence: 95 },
          { id: 'update-status', label: 'Update Status', category: 'action', confidence: 90 },
          { id: 'workflow-complete', label: 'Mark Complete', category: 'output', confidence: 85 }
        );
      } else {
        suggestions.push(
          { id: 'save-to-database', label: 'Save to Database', category: 'action', confidence: 90 },
          { id: 'send-email', label: 'Send Email', category: 'action', confidence: 85 },
          { id: 'conditional-logic', label: 'Add Condition', category: 'logic', confidence: 80 }
        );
      }
    } else if (category === 'logic') {
      suggestions.push(
        { id: 'send-notification', label: 'Send Notification', category: 'action', confidence: 90 },
        { id: 'update-record', label: 'Update Record', category: 'action', confidence: 85 },
        { id: 'workflow-complete', label: 'Complete Workflow', category: 'output', confidence: 95 }
      );
    }

    return suggestions;
  }, [nodes]);

  // Auto-Create and Connect - One-click workflow building
  const autoCreateAndConnect = useCallback((sourceNodeId: string, suggestionId: string, suggestionLabel: string, suggestionCategory: string) => {
    const sourceNode = nodes.find(n => n.id === sourceNodeId);
    if (!sourceNode) return;

    // Create new node positioned to the right of source
    const newNodeId = `${suggestionId}-${Date.now()}`;
    const newNode = {
      id: newNodeId,
      type: 'custom',
      position: {
        x: sourceNode.position.x + 250,
        y: sourceNode.position.y
      },
      data: {
        label: suggestionLabel,
        category: suggestionCategory,
        icon: suggestionCategory === 'action' ? <Zap className="h-4 w-4" /> :
          suggestionCategory === 'logic' ? <GitBranch className="h-4 w-4" /> :
            <CheckCircle className="h-4 w-4" />,
        isConfigured: false
      }
    };

    // Add the new node
    setNodes((nds) => [...nds, newNode]);

    // Create connection
    const newEdge = {
      id: `edge-${sourceNodeId}-${newNodeId}`,
      source: sourceNodeId,
      target: newNodeId,
      type: 'smoothstep',
      animated: true,
      style: {
        stroke: '#0FA4AF',
        strokeWidth: 3
      },
      markerEnd: {
        type: MarkerType.ArrowClosed,
        color: '#0FA4AF'
      }
    };

    setEdges((eds) => addEdge(newEdge, eds));

    // Show success message
    setValidationErrors([`✅ Created "${suggestionLabel}" and connected automatically!`]);
    setTimeout(() => setValidationErrors([]), 3000);
  }, [nodes, setNodes, setEdges]);

  // Real drag connection system
  const startDragConnection = useCallback((nodeId: string, event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();

    const rect = reactFlowWrapper.current?.getBoundingClientRect();
    if (rect) {
      const startPos = {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top,
        nodeId: nodeId
      };

      setDragStart(startPos);
      setDragCurrent(startPos);
      setSourceNode(nodeId);
      setIsDragging(true);
      setConnectionMode('connecting');
    }
  }, []);

  const updateDragConnection = useCallback((event: React.MouseEvent) => {
    if (!isDragging || !dragStart) return;

    const rect = reactFlowWrapper.current?.getBoundingClientRect();
    if (rect) {
      const currentPos = {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top
      };
      setDragCurrent(currentPos);
    }
  }, [isDragging, dragStart]);

  const completeDragConnection = useCallback((targetNodeId: string, event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();

    if (!sourceNode || sourceNode === targetNodeId || !isDragging) {
      cancelConnectionMode();
      return;
    }

    const validation = validateConnection(sourceNode, targetNodeId);

    if (!validation.valid) {
      setValidationErrors([validation.error!]);
      setTimeout(() => setValidationErrors([]), 3000);
      cancelConnectionMode();
      return;
    }

    // Create the connection with toggle functionality
    const newEdge = {
      id: `edge-${sourceNode}-${targetNodeId}-${Date.now()}`,
      source: sourceNode,
      target: targetNodeId,
      type: 'custom',
      animated: false,
      style: {
        stroke: '#964734',
        strokeWidth: 2,
        strokeDasharray: '5,5'
      },
      markerEnd: {
        type: MarkerType.ArrowClosed,
        color: '#964734',
        width: 15,
        height: 15
      },
      data: {
        isActive: false,
        validated: true
      }
    };

    setEdges((eds) => addEdge(newEdge, eds));
    cancelConnectionMode();
  }, [sourceNode, isDragging, validateConnection, setEdges, cancelConnectionMode]);

  // Auto-connection for smart workflows
  const autoConnect = useCallback((sourceId: string) => {
    const suggestions = getSmartSuggestions(sourceId);
    if (suggestions.length === 1) {
      // Auto-connect to the only logical choice
      const mockEvent = new MouseEvent('click') as any;
      setSourceNode(sourceId);
      completeDragConnection(suggestions[0], mockEvent);
      return true;
    }
    return false;
  }, [getSmartSuggestions, completeDragConnection]);

  // WORKING Connection System
  useEffect(() => {
    let currentSource: string | null = null;
    let dragLine: SVGElement | null = null;

    const handleStartConnection = (event: any) => {
      const { sourceNodeId, startX, startY } = event.detail;
      currentSource = sourceNodeId;

      // Create visual drag line
      const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
      svg.style.position = 'fixed';
      svg.style.top = '0';
      svg.style.left = '0';
      svg.style.width = '100vw';
      svg.style.height = '100vh';
      svg.style.pointerEvents = 'none';
      svg.style.zIndex = '9999';

      const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
      line.setAttribute('x1', startX.toString());
      line.setAttribute('y1', startY.toString());
      line.setAttribute('x2', startX.toString());
      line.setAttribute('y2', startY.toString());
      line.setAttribute('stroke', '#0FA4AF');
      line.setAttribute('stroke-width', '3');
      line.setAttribute('stroke-dasharray', '8,4');

      svg.appendChild(line);
      document.body.appendChild(svg);
      dragLine = svg;

      // Track mouse movement
      const handleMouseMove = (e: MouseEvent) => {
        if (line) {
          line.setAttribute('x2', e.clientX.toString());
          line.setAttribute('y2', e.clientY.toString());
        }
      };

      document.addEventListener('mousemove', handleMouseMove);

      // Clean up on mouse up
      const handleMouseUp = () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        if (dragLine) {
          document.body.removeChild(dragLine);
          dragLine = null;
        }
      };

      document.addEventListener('mouseup', handleMouseUp);
    };

    const handleEndConnection = (event: any) => {
      const { targetNodeId } = event.detail;

      if (currentSource && currentSource !== targetNodeId) {
        // Create connection
        const newEdge = {
          id: `edge-${currentSource}-${targetNodeId}-${Date.now()}`,
          source: currentSource,
          target: targetNodeId,
          type: 'smoothstep',
          animated: true,
          style: {
            stroke: '#0FA4AF',
            strokeWidth: 3
          },
          markerEnd: {
            type: MarkerType.ArrowClosed,
            color: '#0FA4AF'
          }
        };

        setEdges((eds) => addEdge(newEdge, eds));
      }

      currentSource = null;
      if (dragLine) {
        document.body.removeChild(dragLine);
        dragLine = null;
      }
    };

    // Auto-Connect Handler
    const handleAutoConnect = (event: any) => {
      const { nodeId } = event.detail;
      const suggestions = getAutoConnectSuggestions(nodeId);

      console.log('Auto-connect triggered for node:', nodeId);
      console.log('Generated suggestions:', suggestions);

      // Always show suggestions, even if empty (with fallback)
      if (suggestions.length === 0) {
        // Fallback suggestions if none found
        const fallbackSuggestions = [
          { id: 'custom-action', label: 'Custom Action', category: 'action', confidence: 80 },
          { id: 'add-condition', label: 'Add Condition', category: 'logic', confidence: 75 },
          { id: 'finish-workflow', label: 'Finish Workflow', category: 'output', confidence: 85 }
        ];
        setAutoSuggestions(fallbackSuggestions);
      } else {
        setAutoSuggestions(suggestions);
      }

      setSourceNode(nodeId);
      setShowAutoConnect(true);
    };

    window.addEventListener('startConnection', handleStartConnection);
    window.addEventListener('endConnection', handleEndConnection);
    window.addEventListener('autoConnect', handleAutoConnect);

    return () => {
      window.removeEventListener('startConnection', handleStartConnection);
      window.removeEventListener('endConnection', handleEndConnection);
      window.removeEventListener('autoConnect', handleAutoConnect);
      if (dragLine) {
        document.body.removeChild(dragLine);
      }
    };
  }, [setEdges, getAutoConnectSuggestions]);

  const selectNodeForConnection = useCallback((nodeId: string) => {
    if (connectionMode === 'selecting') {
      setSourceNode(nodeId);
      setConnectionMode('connecting');
    } else if (connectionMode === 'connecting' && sourceNode && sourceNode !== nodeId) {
      // Create a mock event for the completion
      const mockEvent = new MouseEvent('click') as any;
      completeDragConnection(nodeId, mockEvent);
    }
  }, [connectionMode, sourceNode, completeDragConnection]);

  // React Flow event handlers
  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    // Handle connection mode clicks
    if (connectionMode !== 'off') {
      selectNodeForConnection(node.id);
      return;
    }

    // Normal node selection
    setSelectedNode(node);
  }, [connectionMode, selectNodeForConnection]);

  const onNodeDoubleClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node);
    setIsConfigDialogOpen(true);
  }, []);

  const onDeleteNode = useCallback(() => {
    if (selectedNode) {
      setNodes((nds) => nds.filter((node) => node.id !== selectedNode.id));
      setEdges((eds) => eds.filter((edge) =>
        edge.source !== selectedNode.id && edge.target !== selectedNode.id
      ));
      setSelectedNode(null);
    }
  }, [selectedNode, setNodes, setEdges]);

  const onDuplicateNode = useCallback(() => {
    if (selectedNode) {
      const newNode: Node = {
        ...selectedNode,
        id: `${selectedNode.id}-copy-${Date.now()}`,
        position: {
          x: selectedNode.position.x + 50,
          y: selectedNode.position.y + 50,
        },
      };
      setNodes((nds) => nds.concat(newNode));
    }
  }, [selectedNode, setNodes]);

  // Workflow management
  const saveWorkflow = useCallback(async () => {
    try {
      setIsLoading(true);

      const workflowData = {
        name: workflow.name,
        description: workflow.description,
        is_active: workflow.isActive,
        nodes,
        edges,
        category: workflow.name.includes('Customer') ? 'Customer Service' :
          workflow.name.includes('Lead') ? 'Sales' :
            workflow.name.includes('Content') ? 'Security' : undefined,
      };

      if (currentWorkflow) {
        // Update existing workflow
        const updatedWorkflow = await apiService.updateWorkflow(currentWorkflow.id, workflowData);
        setCurrentWorkflow(updatedWorkflow);
        console.log("Workflow updated:", updatedWorkflow);
      } else {
        // Create new workflow
        const newWorkflow = await apiService.createWorkflow(workflowData);
        setCurrentWorkflow(newWorkflow);
        console.log("Workflow created:", newWorkflow);
      }
    } catch (error) {
      console.error('Failed to save workflow:', error);
    } finally {
      setIsLoading(false);
    }
  }, [workflow, nodes, edges, currentWorkflow]);

  const testWorkflow = useCallback(async () => {
    if (nodes.length === 0) {
      alert('Please add some nodes to test the workflow');
      return;
    }

    try {
      setIsLoading(true);

      // Simulate workflow execution with visual feedback
      const nodeIds = nodes.map(node => node.id);

      // Update nodes to show running status
      setNodes(prevNodes =>
        prevNodes.map(node => ({
          ...node,
          data: { ...node.data, status: 'running' }
        }))
      );

      // Simulate execution delay for each node
      for (let i = 0; i < nodeIds.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 1000));

        setNodes(prevNodes =>
          prevNodes.map((node, index) => ({
            ...node,
            data: {
              ...node.data,
              status: index <= i ? 'success' : index === i + 1 ? 'running' : 'idle'
            }
          }))
        );
      }

      // If we have a current workflow, test it via API
      if (currentWorkflow) {
        const result = await apiService.testWorkflow(currentWorkflow.id);
        console.log("Workflow test result:", result);
        alert(`Workflow test completed successfully! Execution ID: ${result.execution_id}`);
      } else {
        alert('Workflow test completed successfully! All nodes executed without errors.');
      }

    } catch (error) {
      console.error('Failed to test workflow:', error);
      alert('Failed to test workflow. Please check your configuration and try again.');

      // Reset node status on error
      setNodes(prevNodes =>
        prevNodes.map(node => ({
          ...node,
          data: { ...node.data, status: 'error' }
        }))
      );
    } finally {
      setIsLoading(false);

      // Reset status after a delay
      setTimeout(() => {
        setNodes(prevNodes =>
          prevNodes.map(node => ({
            ...node,
            data: { ...node.data, status: 'idle' }
          }))
        );
      }, 2000);
    }
  }, [nodes, currentWorkflow, setNodes]);

  const workflowTemplates: WorkflowTemplate[] = [
    {
      id: "customer-support",
      name: "Customer Support Automation",
      description: "Automatically handle customer inquiries with AI-powered responses and smart routing",
      category: "Customer Service",
      estimatedTime: "5 minutes",
      complexity: "Simple",
      nodes: [
        {
          id: "start",
          type: "start",
          title: "Customer Message Received",
          description: "New message from customer",
          icon: <MessageSquare className="h-5 w-5" />,
          category: "Trigger",
          connections: ["process-1"],
        },
        {
          id: "process-1",
          type: "process",
          title: "Analyze Intent",
          description: "AI analyzes customer intent",
          icon: <Brain className="h-5 w-5" />,
          category: "AI Processing",
          connections: ["decision-1"],
        },
        {
          id: "decision-1",
          type: "decision",
          title: "Can Auto-Respond?",
          description: "Check if we can automatically respond",
          icon: <GitBranch className="h-5 w-5" />,
          category: "Logic",
          connections: ["process-2", "end"],
        },
        {
          id: "process-2",
          type: "process",
          title: "Generate Response",
          description: "AI generates appropriate response",
          icon: <Bot className="h-5 w-5" />,
          category: "AI Processing",
          connections: ["end"],
        },
        {
          id: "end",
          type: "end",
          title: "Send Response",
          description: "Response sent to customer",
          icon: <Mail className="h-5 w-5" />,
          category: "Action",
          connections: [],
        },
      ],
    },
    {
      id: "lead-qualification",
      name: "Lead Qualification Pipeline",
      description: "Automatically qualify and route sales leads based on predefined criteria",
      category: "Sales",
      estimatedTime: "8 minutes",
      complexity: "Medium",
      nodes: [
        {
          id: "start",
          type: "start",
          title: "New Lead Captured",
          description: "Lead form submitted",
          icon: <Users className="h-5 w-5" />,
          category: "Trigger",
          connections: ["process-1"],
        },
        {
          id: "process-1",
          type: "process",
          title: "Enrich Lead Data",
          description: "Gather additional lead information",
          icon: <Database className="h-5 w-5" />,
          category: "Data Processing",
          connections: ["decision-1"],
        },
        {
          id: "decision-1",
          type: "decision",
          title: "Qualified Lead?",
          description: "Check lead qualification criteria",
          icon: <BarChart3 className="h-5 w-5" />,
          category: "Logic",
          connections: ["process-2", "process-3"],
        },
        {
          id: "process-2",
          type: "process",
          title: "Route to Sales",
          description: "Assign to sales representative",
          icon: <ArrowRight className="h-5 w-5" />,
          category: "Action",
          connections: ["end"],
        },
        {
          id: "process-3",
          type: "process",
          title: "Add to Nurture Campaign",
          description: "Add to email nurture sequence",
          icon: <Mail className="h-5 w-5" />,
          category: "Marketing",
          connections: ["end"],
        },
        {
          id: "end",
          type: "end",
          title: "Process Complete",
          description: "Lead processed successfully",
          icon: <Shield className="h-5 w-5" />,
          category: "Completion",
          connections: [],
        },
      ],
    },
    {
      id: "content-moderation",
      name: "Content Moderation System",
      description: "Automatically moderate user-generated content using AI and human review",
      category: "Security",
      estimatedTime: "3 minutes",
      complexity: "Advanced",
      nodes: [
        {
          id: "start",
          type: "start",
          title: "Content Submitted",
          description: "User submits content",
          icon: <FileText className="h-5 w-5" />,
          category: "Trigger",
          connections: ["process-1"],
        },
        {
          id: "process-1",
          type: "process",
          title: "AI Content Analysis",
          description: "Analyze content for violations",
          icon: <Brain className="h-5 w-5" />,
          category: "AI Processing",
          connections: ["decision-1"],
        },
        {
          id: "decision-1",
          type: "decision",
          title: "Content Safe?",
          description: "Check if content meets guidelines",
          icon: <Shield className="h-5 w-5" />,
          category: "Security",
          connections: ["process-2", "process-3"],
        },
        {
          id: "process-2",
          type: "process",
          title: "Approve Content",
          description: "Content approved for publication",
          icon: <ArrowRight className="h-5 w-5" />,
          category: "Action",
          connections: ["end"],
        },
        {
          id: "process-3",
          type: "process",
          title: "Flag for Review",
          description: "Send to human moderator",
          icon: <Users className="h-5 w-5" />,
          category: "Review",
          connections: ["end"],
        },
        {
          id: "end",
          type: "end",
          title: "Moderation Complete",
          description: "Content moderation finished",
          icon: <Shield className="h-5 w-5" />,
          category: "Completion",
          connections: [],
        },
      ],
    },
  ];

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case "Simple": return "bg-green-100 text-green-800 border-green-200";
      case "Medium": return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "Advanced": return "bg-red-100 text-red-800 border-red-200";
      default: return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "Customer Service": return <MessageSquare className="h-6 w-6" />;
      case "Sales": return <BarChart3 className="h-6 w-6" />;
      case "Security": return <Shield className="h-6 w-6" />;
      case "Marketing": return <Mail className="h-6 w-6" />;
      default: return <Workflow className="h-6 w-6" />;
    }
  };

  const getCategoryGradient = (category: string) => {
    switch (category) {
      case "Customer Service": return "from-[#003135] to-[#024950]";
      case "Sales": return "from-[#024950] to-[#0FA4AF]";
      case "Security": return "from-[#964734] to-[#024950]";
      case "Marketing": return "from-[#0FA4AF] to-[#AFDDE5]";
      default: return "from-[#003135] to-[#024950]";
    }
  };

  const handleTemplateSelect = (template: ApiWorkflowTemplate) => {
    // Convert API template to local template format for preview
    const localTemplate: WorkflowTemplate = {
      id: template.id.toString(),
      name: template.name,
      description: template.description,
      category: template.category,
      estimatedTime: template.estimated_time,
      complexity: template.complexity,
      nodes: template.nodes.map((node: any) => ({
        id: node.id,
        type: node.type,
        title: node.title,
        description: node.description,
        icon: null, // Will be set by the component
        category: node.category,
        connections: node.connections || [],
      })),
    };

    setSelectedTemplate(localTemplate);
    setCurrentView("preview");
  };

  const handleUseTemplate = async () => {
    if (!selectedTemplate) return;

    try {
      setIsLoading(true);

      // Find the API template that matches the selected template
      const apiTemplate = apiTemplates.find(t => t.name === selectedTemplate.name);
      if (!apiTemplate) {
        console.error('API template not found');
        return;
      }

      // Create workflow from template
      const workflowData = await apiService.createWorkflowFromTemplate(apiTemplate.id, {
        name: `${selectedTemplate.name} - Copy`,
        description: selectedTemplate.description,
      });

      // Set the current workflow
      setCurrentWorkflow(workflowData);

      // Convert API workflow to React Flow format
      setNodes(workflowData.nodes || []);
      setEdges(workflowData.edges || []);

      // Update workflow state
      setWorkflow({
        name: workflowData.name,
        description: workflowData.description || "",
        isActive: workflowData.is_active,
        nodes: workflowData.nodes || [],
        edges: workflowData.edges || [],
      });

      setCurrentView("builder");
    } catch (error) {
      console.error('Failed to create workflow from template:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const renderTemplateGallery = () => (
    <div className="p-6" data-tour="welcome">
      {/* Page Header - Inline Breadcrumb Style */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <Workflow className="h-5 w-5 text-[#024950]" />
          <h1 className="text-xl font-semibold text-[#003135] dark:text-white">Workflow Templates</h1>
        </div>
        <Button
          variant="outline"
          onClick={() => setCurrentView("builder")}
          className="border-[#0FA4AF]/30 text-[#024950] hover:bg-[#0FA4AF]/10 dark:text-[#AFDDE5] dark:border-[#024950] dark:hover:bg-[#024950]/50"
          data-tour="create-custom"
        >
          <Plus className="h-4 w-4 mr-2" />
          Create Custom
        </Button>
      </div>

      <div className="space-y-8">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-[#024950] to-[#0FA4AF] rounded-2xl mb-6">
            <Workflow className="h-10 w-10 text-white" />
          </div>
          <h1 className="text-5xl font-bold text-[#003135] dark:text-white mb-6">
            Workflow Templates
          </h1>
          <p className="text-xl text-[#024950] dark:text-[#AFDDE5] max-w-3xl mx-auto leading-relaxed">
            Choose from our professionally designed workflow templates.
            Each template is optimized for specific business processes and can be customized to match your exact requirements.
          </p>
        </div>

        {/* Template Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16" data-tour="template-gallery">
          {isLoading ? (
            <div className="col-span-full text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#0FA4AF] mx-auto"></div>
              <p className="mt-4 text-[#024950] dark:text-[#AFDDE5]">Loading templates...</p>
            </div>
          ) : apiTemplates.length > 0 ? (
            apiTemplates.map((template, index) => (
              <div
                key={template.id}
                className="group bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 cursor-pointer border border-gray-100 overflow-hidden"
                onClick={() => handleTemplateSelect(template)}
                data-tour={index === 0 ? "template-card" : undefined}
              >
                {/* Template Header */}
                <div className={`h-32 bg-gradient-to-r ${getCategoryGradient(template.category)} relative overflow-hidden`}>
                  <div className="absolute inset-0 bg-black/10"></div>
                  <div className="relative p-6 h-full flex items-center justify-between">
                    <div className="text-white">
                      <h3 className="font-bold text-xl mb-1">{template.name}</h3>
                      <p className="text-white/90 text-sm">{template.category}</p>
                    </div>
                    <div className="text-white/90">
                      {getCategoryIcon(template.category)}
                    </div>
                  </div>
                </div>

                {/* Template Content */}
                <div className="p-6">
                  <p className="text-gray-600 mb-6 leading-relaxed text-sm">
                    {template.description}
                  </p>

                  {/* Metadata */}
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-600">{template.estimated_time}</span>
                      </div>
                      <Badge className={`text-xs px-3 py-1 border ${getComplexityColor(template.complexity)}`}>
                        {template.complexity}
                      </Badge>
                    </div>
                  </div>

                  {/* Footer */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500 font-medium">
                      {template.nodes.length} steps
                    </span>
                    <Button
                      size="sm"
                      className={`bg-gradient-to-r ${getCategoryGradient(template.category)} hover:shadow-lg transition-all duration-300 group-hover:scale-105`}
                    >
                      Preview <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="col-span-full text-center py-12">
              <p className="text-[#024950] dark:text-[#AFDDE5]">No templates available</p>
            </div>
          )}
        </div>

        {/* Create Custom Button */}
        <div className="text-center">
          <Button
            size="lg"
            variant="outline"
            className="px-12 py-6 text-lg border-2 border-dashed border-[#0FA4AF]/30 hover:border-[#0FA4AF] hover:bg-[#0FA4AF]/10 dark:hover:bg-[#024950]/50 transition-all duration-300 rounded-2xl text-[#024950] dark:text-[#AFDDE5]"
            onClick={() => setCurrentView("builder")}
          >
            <Plus className="h-6 w-6 mr-3" />
            Create Custom Workflow
          </Button>
        </div>
      </div>
    </div>
  );

  const renderTemplatePreview = () => {
    if (!selectedTemplate) return null;

    return (
      <div className="p-6" data-tour="template-preview">
        {/* Page Header - Inline Breadcrumb Style */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              onClick={() => setCurrentView("templates")}
              className="flex items-center space-x-2 text-[#024950] dark:text-[#AFDDE5] hover:bg-[#0FA4AF]/10 dark:hover:bg-[#024950]/50"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>Back to Templates</span>
            </Button>
            <span className="text-[#024950]/50 dark:text-[#AFDDE5]/50">/</span>
            <h1 className="text-xl font-semibold text-[#003135] dark:text-white">{selectedTemplate.name}</h1>
          </div>
          <div className="flex space-x-4">
            <Button variant="outline" size="lg" className="border-[#0FA4AF]/30 text-[#024950] hover:bg-[#0FA4AF]/10 dark:text-[#AFDDE5] dark:border-[#024950] dark:hover:bg-[#024950]/50">
              <Save className="h-4 w-4 mr-2" />
              Save Template
            </Button>
            <Button size="lg" onClick={handleUseTemplate} className="bg-gradient-to-r from-[#024950] to-[#0FA4AF] text-white hover:from-[#003135] hover:to-[#024950]" data-tour="use-template-btn">
              <Play className="h-4 w-4 mr-2" />
              Use This Template
            </Button>
          </div>
        </div>

        <div className="space-y-8">

          {/* Template Info */}
          <div className="bg-white rounded-3xl shadow-xl p-8 mb-8">
            <div className="flex items-start justify-between mb-6">
              <div className="flex items-center space-x-4">
                <div className={`p-4 bg-gradient-to-r ${getCategoryGradient(selectedTemplate.category)} rounded-2xl text-white`}>
                  {getCategoryIcon(selectedTemplate.category)}
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">
                    {selectedTemplate.name}
                  </h1>
                  <p className="text-gray-600 text-lg">
                    {selectedTemplate.description}
                  </p>
                </div>
              </div>
              <div className="flex flex-col items-end space-y-2">
                <Badge className={`px-4 py-2 border ${getComplexityColor(selectedTemplate.complexity)}`}>
                  {selectedTemplate.complexity}
                </Badge>
                <div className="flex items-center space-x-2 text-gray-500">
                  <Clock className="h-4 w-4" />
                  <span>{selectedTemplate.estimatedTime}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Workflow Visualization */}
          <div className="bg-white rounded-3xl shadow-xl p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-8">Workflow Steps</h2>
            <div className="space-y-6">
              {selectedTemplate.nodes.map((node, index) => (
                <div key={node.id} className="flex items-center space-x-6">
                  {/* Step Number */}
                  <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-[#024950] to-[#0FA4AF] rounded-full flex items-center justify-center text-white font-bold">
                    {index + 1}
                  </div>

                  {/* Step Card */}
                  <div className="flex-1 bg-gray-50 rounded-2xl p-6 border border-gray-200">
                    <div className="flex items-center space-x-4">
                      <div className="p-3 bg-white rounded-xl shadow-sm">
                        {node.icon}
                      </div>
                      <div className="flex-1">
                        <h3 className="font-bold text-lg text-gray-900 mb-1">
                          {node.title}
                        </h3>
                        <p className="text-gray-600 mb-2">
                          {node.description}
                        </p>
                        <Badge variant="secondary" className="text-xs">
                          {node.category}
                        </Badge>
                      </div>
                      <div className="text-gray-400">
                        {node.type === "decision" && <GitBranch className="h-6 w-6" />}
                        {node.type === "process" && <Zap className="h-6 w-6" />}
                        {node.type === "start" && <Play className="h-6 w-6" />}
                        {node.type === "end" && <Shield className="h-6 w-6" />}
                      </div>
                    </div>
                  </div>

                  {/* Arrow */}
                  {index < selectedTemplate.nodes.length - 1 && (
                    <div className="flex-shrink-0">
                      <ArrowRight className="h-6 w-6 text-gray-400" />
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderWorkflowBuilder = () => (
    <ReactFlowProvider>
      <div className="h-screen flex flex-col bg-gradient-to-br from-[#AFDDE5]/10 to-[#964734]/5">
        {/* Enhanced Header */}
        <div className="workflow-header flex items-center justify-between p-6 bg-white/95 dark:bg-[#003135]/95 backdrop-blur-sm border-b border-[#0FA4AF]/20 dark:border-[#024950] shadow-sm" data-tour="workflow-header">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setCurrentView("templates")}
              className="text-[#964734] dark:text-[#AFDDE5] hover:bg-[#964734]/10 dark:hover:bg-[#964734]/20 transition-all duration-300 group"
            >
              <ArrowLeft className="h-4 w-4 mr-2 group-hover:-translate-x-1 transition-transform duration-300" />
              Back to Templates
            </Button>
            <div className="h-6 w-px bg-[#964734]/30"></div>
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-[#964734] to-[#024950] rounded-lg flex items-center justify-center">
                <Workflow className="h-4 w-4 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-bold text-[#003135] dark:text-white">
                  {workflow.name}
                </h1>
                <p className="text-xs text-[#964734] dark:text-[#AFDDE5]">
                  Drag & Drop Workflow Builder
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {selectedNode && (
              <div className="flex items-center space-x-2 px-3 py-1 bg-[#964734]/10 rounded-lg border border-[#964734]/30">
                <span className="text-sm text-[#964734] font-medium">
                  {selectedNode.data.label} selected
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onDuplicateNode}
                  className="h-6 w-6 p-0 hover:bg-[#964734]/20"
                >
                  <Copy className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onDeleteNode}
                  className="h-6 w-6 p-0 hover:bg-red-500/20 text-red-500"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            )}

            {/* Animated Demo Trigger */}
            <AnimatedDemoTrigger
              module="workflow-builder"
              position="header"
            />

            <Button
              variant="outline"
              size="sm"
              onClick={saveWorkflow}
              className="border-[#964734]/30 dark:border-[#024950] hover:bg-[#964734]/10 dark:hover:bg-[#964734]/20 text-[#964734] transition-all duration-300 group"
            >
              <Save className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform duration-300" />
              Save Draft
            </Button>
            <Button
              size="sm"
              onClick={testWorkflow}
              className="bg-gradient-to-r from-[#964734] to-[#024950] hover:from-[#024950] hover:to-[#0FA4AF] text-white shadow-lg hover:shadow-xl transition-all duration-300 group"
            >
              <Play className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform duration-300" />
              Test Workflow
            </Button>
          </div>
        </div>

        {/* Simple Connection Toolbar for Non-Technical Users */}
        <div className="bg-white/95 dark:bg-[#003135]/95 backdrop-blur-sm border-b border-[#0FA4AF]/20 dark:border-[#024950] px-6 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-6 h-6 bg-gradient-to-r from-[#964734] to-[#0FA4AF] rounded-lg flex items-center justify-center">
                  <Link className="h-3 w-3 text-white" />
                </div>
                <span className="text-sm font-medium text-[#003135] dark:text-white">Connect Nodes</span>
              </div>

              {connectionMode === 'off' && (
                <Button
                  size="sm"
                  onClick={startConnectionMode}
                  className="bg-gradient-to-r from-[#964734] to-[#024950] hover:from-[#024950] hover:to-[#0FA4AF] text-white"
                >
                  <Link className="h-4 w-4 mr-2" />
                  Start Connecting
                </Button>
              )}

              {connectionMode === 'selecting' && (
                <div className="flex items-center space-x-3">
                  <div className="px-3 py-1 bg-[#964734]/10 rounded-lg border border-[#964734]/30">
                    <span className="text-sm text-[#964734] font-medium">Step 1: Click the first node</span>
                  </div>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={cancelConnectionMode}
                    className="border-red-300 text-red-600 hover:bg-red-50"
                  >
                    Cancel
                  </Button>
                </div>
              )}

              {connectionMode === 'connecting' && sourceNode && (
                <div className="flex items-center space-x-3">
                  <div className="px-3 py-1 bg-[#0FA4AF]/10 rounded-lg border border-[#0FA4AF]/30">
                    <span className="text-sm text-[#0FA4AF] font-medium">Step 2: Click the second node to connect</span>
                  </div>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={cancelConnectionMode}
                    className="border-red-300 text-red-600 hover:bg-red-50"
                  >
                    Cancel
                  </Button>
                </div>
              )}
            </div>

            {/* Connection Instructions */}
            <div className="flex items-center space-x-4 text-xs text-[#024950] dark:text-[#AFDDE5]">
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-[#0FA4AF] rounded-full"></div>
                <span>Triggers start workflows</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-[#024950] rounded-full"></div>
                <span>Actions perform tasks</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-[#964734] rounded-full"></div>
                <span>Logic controls flow</span>
              </div>
            </div>
          </div>
        </div>

        {/* Connection Helper Overlay */}
        {showConnectionHelper && (
          <div className="absolute top-20 left-1/2 transform -translate-x-1/2 z-50 bg-white dark:bg-[#003135] border border-[#964734]/30 rounded-lg shadow-xl p-4 max-w-md">
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-[#964734] to-[#0FA4AF] rounded-lg flex items-center justify-center flex-shrink-0">
                <Link className="h-4 w-4 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-[#003135] dark:text-white mb-2">How to Connect Nodes</h3>
                <div className="space-y-2 text-sm text-[#024950] dark:text-[#AFDDE5]">
                  <div className="flex items-center space-x-2">
                    <span className="w-5 h-5 bg-[#964734] text-white rounded-full flex items-center justify-center text-xs font-bold">1</span>
                    <span>Click "Start Connecting" button</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="w-5 h-5 bg-[#964734] text-white rounded-full flex items-center justify-center text-xs font-bold">2</span>
                    <span>Click the first node (where data comes from)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="w-5 h-5 bg-[#964734] text-white rounded-full flex items-center justify-center text-xs font-bold">3</span>
                    <span>Click the second node (where data goes to)</span>
                  </div>
                </div>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setShowConnectionHelper(false)}
                  className="mt-3 text-[#964734] hover:bg-[#964734]/10"
                >
                  Got it!
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Auto-Connect Suggestions Popup */}
        {showAutoConnect && sourceNode && autoSuggestions.length > 0 && (
          <div className="absolute top-32 left-1/2 transform -translate-x-1/2 z-50 bg-white dark:bg-[#003135] border border-[#964734]/30 rounded-xl shadow-2xl p-6 max-w-lg backdrop-blur-sm">
            <div className="flex items-start space-x-3 mb-4">
              <div className="w-10 h-10 bg-gradient-to-r from-[#964734] to-[#0FA4AF] rounded-lg flex items-center justify-center flex-shrink-0">
                <Zap className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="font-bold text-[#003135] dark:text-white mb-1">Smart Suggestions</h3>
                <p className="text-sm text-[#024950] dark:text-[#AFDDE5]">
                  What would you like to add after "{nodes.find(n => n.id === sourceNode)?.data.label}"?
                </p>
              </div>
              <button
                onClick={() => setShowAutoConnect(false)}
                className="ml-auto p-1 hover:bg-[#964734]/10 rounded"
              >
                <X className="h-4 w-4 text-[#964734]" />
              </button>
            </div>

            <div className="space-y-2">
              {autoSuggestions.map((suggestion, index) => (
                <button
                  key={suggestion.id}
                  onClick={() => {
                    autoCreateAndConnect(sourceNode, suggestion.id, suggestion.label, suggestion.category);
                    setShowAutoConnect(false);
                    setAutoSuggestions([]);
                  }}
                  className="w-full flex items-center space-x-3 p-3 bg-gradient-to-r from-[#0FA4AF]/5 to-[#964734]/5 hover:from-[#0FA4AF]/10 hover:to-[#964734]/10 rounded-lg border border-[#0FA4AF]/20 hover:border-[#964734]/30 transition-all duration-200 group"
                >
                  <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${suggestion.category === 'action' ? 'bg-[#0FA4AF]/20' :
                    suggestion.category === 'logic' ? 'bg-[#964734]/20' :
                      'bg-[#024950]/20'
                    }`}>
                    {suggestion.category === 'action' ? <Zap className="h-4 w-4 text-[#0FA4AF]" /> :
                      suggestion.category === 'logic' ? <GitBranch className="h-4 w-4 text-[#964734]" /> :
                        <CheckCircle className="h-4 w-4 text-[#024950]" />}
                  </div>
                  <div className="flex-1 text-left">
                    <div className="font-semibold text-[#003135] dark:text-white group-hover:text-[#964734] transition-colors">
                      {suggestion.label}
                    </div>
                    <div className="text-xs text-[#024950] dark:text-[#AFDDE5] capitalize">
                      {suggestion.category} • {suggestion.confidence}% match
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Plus className="h-4 w-4 text-[#964734]" />
                    <ArrowRight className="h-4 w-4 text-[#0FA4AF]" />
                  </div>
                </button>
              ))}
            </div>

            <div className="mt-4 pt-3 border-t border-[#0FA4AF]/20">
              <p className="text-xs text-[#024950] dark:text-[#AFDDE5] text-center">
                💡 Click any suggestion to create and connect automatically
              </p>
            </div>
          </div>
        )}

        {/* Connection Instructions */}
        {connectionMode === 'selecting' && sourceNode && (
          <div className="absolute top-32 left-1/2 transform -translate-x-1/2 z-50 bg-[#0FA4AF]/10 dark:bg-[#0FA4AF]/20 border border-[#0FA4AF]/30 rounded-lg shadow-xl p-4 max-w-md backdrop-blur-sm">
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-[#0FA4AF] rounded-lg flex items-center justify-center flex-shrink-0">
                <Link className="h-4 w-4 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-[#003135] dark:text-white mb-2">Creating Connection</h3>
                <p className="text-sm text-[#024950] dark:text-[#AFDDE5]">
                  Click on any <span className="font-semibold text-[#964734]">brown circle</span> to complete the connection from "{nodes.find(n => n.id === sourceNode)?.data.label}"
                </p>
                <div className="flex items-center space-x-2 mt-2">
                  <div className="w-3 h-3 bg-[#0FA4AF] rounded-full animate-pulse"></div>
                  <span className="text-xs text-[#024950] dark:text-[#AFDDE5]">Follow the blue line</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Validation Error Display */}
        {validationErrors.length > 0 && (
          <div className="absolute top-32 left-1/2 transform -translate-x-1/2 z-50 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg shadow-xl p-4 max-w-md">
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center flex-shrink-0">
                <AlertCircle className="h-4 w-4 text-white" />
              </div>
              <div>
                <h3 className="font-semibold text-red-800 dark:text-red-200 mb-2">Connection Error</h3>
                <div className="space-y-1">
                  {validationErrors.map((error, index) => (
                    <p key={index} className="text-sm text-red-700 dark:text-red-300">{error}</p>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Visual Connection Preview */}
        {previewConnection && (
          <svg className="absolute inset-0 pointer-events-none z-40" style={{ width: '100%', height: '100%' }}>
            <defs>
              <marker
                id="preview-arrow"
                markerWidth="12"
                markerHeight="8"
                refX="11"
                refY="4"
                orient="auto"
              >
                <polygon points="0 0, 12 4, 0 8" fill="#0FA4AF" />
              </marker>
            </defs>
            <path
              d={`M ${getNodePosition(previewConnection.from)?.x || 0} ${getNodePosition(previewConnection.from)?.y || 0} L ${previewConnection.to.x} ${previewConnection.to.y}`}
              stroke="#0FA4AF"
              strokeWidth="3"
              strokeDasharray="8,4"
              fill="none"
              markerEnd="url(#preview-arrow)"
              className="animate-pulse"
              style={{
                filter: 'drop-shadow(0 0 6px rgba(15, 164, 175, 0.6))'
              }}
            />
            {/* Connection start indicator */}
            <circle
              cx={getNodePosition(previewConnection.from)?.x || 0}
              cy={getNodePosition(previewConnection.from)?.y || 0}
              r="6"
              fill="#0FA4AF"
              className="animate-pulse"
            />
            {/* Connection end indicator */}
            <circle
              cx={previewConnection.to.x}
              cy={previewConnection.to.y}
              r="4"
              fill="#964734"
              className="animate-pulse"
            />
          </svg>
        )}

        {/* Main Content Area */}
        <div className="flex-1 flex overflow-hidden">
          {/* Enhanced Node Palette */}
          <div className="node-palette w-80 bg-white/95 dark:bg-[#003135]/95 backdrop-blur-sm border-r border-[#0FA4AF]/20 dark:border-[#024950] p-4 overflow-y-auto" data-tour="node-palette">
            <div className="space-y-6">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-8 h-8 bg-gradient-to-r from-[#0FA4AF] to-[#964734] rounded-lg flex items-center justify-center">
                  <Plus className="h-4 w-4 text-white" />
                </div>
                <div>
                  <h2 className="text-lg font-bold text-[#003135] dark:text-white">Node Palette</h2>
                  <p className="text-xs text-[#964734] dark:text-[#AFDDE5]">Drag to canvas</p>
                </div>
              </div>

              {/* Trigger Nodes */}
              <div>
                <h3 className="font-semibold text-[#003135] dark:text-white mb-3 flex items-center">
                  <div className="w-2 h-2 bg-[#0FA4AF] rounded-full mr-2"></div>
                  Triggers
                </h3>
                <div className="space-y-2">
                  {nodeTypes.filter(node => node.category === "trigger").map((nodeType) => (
                    <DraggableNode
                      key={nodeType.id}
                      nodeType={nodeType}
                      onDragStart={onDragStart}
                    />
                  ))}
                </div>
              </div>

              {/* Action Nodes */}
              <div>
                <h3 className="font-semibold text-[#003135] dark:text-white mb-3 flex items-center">
                  <div className="w-2 h-2 bg-[#024950] rounded-full mr-2"></div>
                  Actions
                </h3>
                <div className="space-y-2">
                  {nodeTypes.filter(node => node.category === "action").map((nodeType) => (
                    <DraggableNode
                      key={nodeType.id}
                      nodeType={nodeType}
                      onDragStart={onDragStart}
                    />
                  ))}
                </div>
              </div>

              {/* Logic Nodes */}
              <div>
                <h3 className="font-semibold text-[#003135] dark:text-white mb-3 flex items-center">
                  <div className="w-2 h-2 bg-[#964734] rounded-full mr-2"></div>
                  Logic & Control
                </h3>
                <div className="space-y-2">
                  {nodeTypes.filter(node => node.category === "logic").map((nodeType) => (
                    <DraggableNode
                      key={nodeType.id}
                      nodeType={nodeType}
                      onDragStart={onDragStart}
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* ReactFlow Canvas */}
          <div className="workflow-canvas flex-1 relative" data-tour="canvas">
            <div
              ref={reactFlowWrapper}
              className="w-full h-full"
              onDrop={onDrop}
              onDragOver={onDragOver}
            >
              <ReactFlow
                nodes={enhancedNodes}
                edges={edges}
                onNodesChange={onNodesChange}
                onEdgesChange={onEdgesChange}
                onConnect={onConnect}
                onNodeClick={onNodeClick}
                onNodeDoubleClick={onNodeDoubleClick}
                onInit={setReactFlowInstance}
                nodeTypes={customNodeTypes}
                edgeTypes={customEdgeTypes}
                fitView
                className="bg-gradient-to-br from-[#AFDDE5]/5 to-[#964734]/5"
                onMouseMove={updateMousePosition}
              >
                <Background
                  color="#964734"
                  gap={20}
                  size={1}
                />
                <Controls
                  className="bg-white/95 dark:bg-[#003135]/95 backdrop-blur-sm border border-[#964734]/30 rounded-lg shadow-lg"
                  data-tour="controls"
                />
                <MiniMap
                  className="bg-white/95 dark:bg-[#003135]/95 backdrop-blur-sm border border-[#964734]/30 rounded-lg shadow-lg"
                  data-tour="minimap"
                  nodeColor={(node) => {
                    switch (node.data.category) {
                      case "trigger": return "#0FA4AF";
                      case "action": return "#024950";
                      case "logic": return "#964734";
                      default: return "#AFDDE5";
                    }
                  }}
                />
              </ReactFlow>

              {/* Empty State */}
              {nodes.length === 0 && (
                <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                  <div className="text-center text-[#024950]/60 dark:text-[#AFDDE5]/60 max-w-md">
                    <div className="w-16 h-16 bg-gradient-to-r from-[#964734]/20 to-[#0FA4AF]/20 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Workflow className="h-8 w-8 text-[#964734]" />
                    </div>
                    <h3 className="text-lg font-semibold text-[#003135] dark:text-white mb-2">
                      Start Building Your Workflow
                    </h3>
                    <p className="text-sm mb-4">
                      Drag nodes from the palette to create your automation workflow
                    </p>
                    <div className="flex items-center justify-center space-x-4 text-xs">
                      <div className="flex items-center space-x-1">
                        <MousePointer className="h-3 w-3" />
                        <span>Drag & Drop</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <ArrowRight className="h-3 w-3" />
                        <span>Connect</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Settings className="h-3 w-3" />
                        <span>Configure</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Properties Panel */}
          <div className="col-span-3 space-y-4">
            <Card className="bg-white dark:bg-[#003135] border border-[#0FA4AF]/20 dark:border-[#024950]">
              <CardHeader>
                <CardTitle className="text-lg text-[#003135] dark:text-white">Properties</CardTitle>
                <CardDescription className="text-[#024950] dark:text-[#AFDDE5]">
                  Configure selected node
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center text-[#024950]/60 dark:text-[#AFDDE5]/60 py-8">
                  <Settings className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">Select a node to configure its properties</p>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-[#003135] border border-[#0FA4AF]/20 dark:border-[#024950]">
              <CardHeader>
                <CardTitle className="text-lg text-[#003135] dark:text-white">Workflow Info</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="workflow-name" className="text-[#024950] dark:text-[#AFDDE5]">Name</Label>
                  <Input
                    id="workflow-name"
                    placeholder="Enter workflow name"
                    className="border-[#0FA4AF]/30 focus:border-[#0FA4AF]"
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Enhanced Properties Panel */}
          <div className="properties-panel w-80 bg-white/95 dark:bg-[#003135]/95 backdrop-blur-sm border-l border-[#0FA4AF]/20 dark:border-[#024950] p-4 overflow-y-auto" data-tour="properties-panel">
            <div className="space-y-6">
              {/* Node Properties */}
              <div>
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-8 h-8 bg-gradient-to-r from-[#964734] to-[#024950] rounded-lg flex items-center justify-center">
                    <Settings className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <h2 className="text-lg font-bold text-[#003135] dark:text-white">Properties</h2>
                    <p className="text-xs text-[#964734] dark:text-[#AFDDE5]">Configure node</p>
                  </div>
                </div>

                {selectedNode ? (
                  <div className="space-y-4">
                    <div className="p-4 bg-gradient-to-r from-[#964734]/10 to-[#0FA4AF]/10 rounded-lg border border-[#964734]/30">
                      <div className="flex items-center space-x-3 mb-3">
                        {selectedNode.data.icon}
                        <div>
                          <h3 className="font-semibold text-[#003135] dark:text-white">
                            {selectedNode.data.label}
                          </h3>
                          <p className="text-xs text-[#964734] dark:text-[#AFDDE5]">
                            {selectedNode.data.description}
                          </p>
                        </div>
                      </div>
                      <Button
                        size="sm"
                        onClick={() => setIsConfigDialogOpen(true)}
                        className="w-full bg-gradient-to-r from-[#964734] to-[#024950] hover:from-[#024950] hover:to-[#0FA4AF] text-white"
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        Configure Node
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="text-center text-[#024950]/60 dark:text-[#AFDDE5]/60 py-8">
                    <div className="w-12 h-12 bg-[#964734]/20 rounded-full flex items-center justify-center mx-auto mb-3">
                      <MousePointer className="h-6 w-6 text-[#964734]" />
                    </div>
                    <p className="text-sm">Select a node to configure its properties</p>
                  </div>
                )}
              </div>

              {/* Workflow Info */}
              <div>
                <h3 className="font-semibold text-[#003135] dark:text-white mb-4 flex items-center">
                  <div className="w-2 h-2 bg-[#964734] rounded-full mr-2"></div>
                  Workflow Settings
                </h3>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="workflow-name" className="text-[#024950] dark:text-[#AFDDE5]">Name</Label>
                    <Input
                      id="workflow-name"
                      value={workflow.name}
                      onChange={(e) => setWorkflow(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Enter workflow name"
                      className="border-[#964734]/30 focus:border-[#964734] focus:ring-[#964734]/20"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="workflow-description" className="text-[#024950] dark:text-[#AFDDE5]">Description</Label>
                    <Textarea
                      id="workflow-description"
                      value={workflow.description}
                      onChange={(e) => setWorkflow(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Describe your workflow"
                      rows={3}
                      className="border-[#964734]/30 focus:border-[#964734] focus:ring-[#964734]/20"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-[#024950] dark:text-[#AFDDE5]">Status</Label>
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={workflow.isActive}
                        onCheckedChange={(checked) => setWorkflow(prev => ({ ...prev, isActive: checked }))}
                      />
                      <span className="text-sm text-[#024950] dark:text-[#AFDDE5]">
                        {workflow.isActive ? "Active" : "Inactive"}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Workflow Stats */}
              <div className="p-4 bg-gradient-to-r from-[#0FA4AF]/10 to-[#964734]/10 rounded-lg border border-[#0FA4AF]/30">
                <h4 className="font-medium text-[#003135] dark:text-white mb-3">Workflow Stats</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-[#024950] dark:text-[#AFDDE5]">Nodes:</span>
                    <span className="font-medium text-[#964734]">{nodes.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-[#024950] dark:text-[#AFDDE5]">Connections:</span>
                    <span className="font-medium text-[#964734]">{edges.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-[#024950] dark:text-[#AFDDE5]">Status:</span>
                    <Badge className={workflow.isActive ? "bg-[#0FA4AF]/20 text-[#0FA4AF]" : "bg-gray-500/20 text-gray-500"}>
                      {workflow.isActive ? "Active" : "Draft"}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Node Configuration Dialog */}
        <Dialog open={isConfigDialogOpen} onOpenChange={setIsConfigDialogOpen}>
          <DialogContent className="max-w-2xl bg-white dark:bg-[#003135] border-[#964734]/30">
            <DialogHeader>
              <DialogTitle className="text-[#003135] dark:text-white flex items-center">
                {selectedNode?.data.icon}
                <span className="ml-2">Configure {selectedNode?.data.label}</span>
              </DialogTitle>
              <DialogDescription className="text-[#024950] dark:text-[#AFDDE5]">
                Set up the configuration for this node
              </DialogDescription>
            </DialogHeader>

            {selectedNode && (
              <div className="space-y-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-[#024950] dark:text-[#AFDDE5]">Node Name</Label>
                    <Input
                      value={selectedNode.data.label}
                      className="border-[#964734]/30 focus:border-[#964734]"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-[#024950] dark:text-[#AFDDE5]">Category</Label>
                    <Select value={selectedNode.data.category}>
                      <SelectTrigger className="border-[#964734]/30">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="trigger">Trigger</SelectItem>
                        <SelectItem value="action">Action</SelectItem>
                        <SelectItem value="logic">Logic</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-[#024950] dark:text-[#AFDDE5]">Description</Label>
                  <Textarea
                    value={selectedNode.data.description || ""}
                    placeholder="Enter node description"
                    className="border-[#964734]/30 focus:border-[#964734]"
                  />
                </div>

                <div className="flex justify-end space-x-2 pt-4">
                  <Button
                    variant="outline"
                    onClick={() => setIsConfigDialogOpen(false)}
                    className="border-[#964734]/30 text-[#964734] hover:bg-[#964734]/10"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={() => {
                      // Here you would update the node configuration
                      setIsConfigDialogOpen(false);
                    }}
                    className="bg-gradient-to-r from-[#964734] to-[#024950] hover:from-[#024950] hover:to-[#0FA4AF] text-white"
                  >
                    Save Configuration
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </ReactFlowProvider>
  );

  // Main render logic
  const renderContent = () => {
    if (currentView === "preview") {
      return renderTemplatePreview();
    }

    if (currentView === "builder") {
      return renderWorkflowBuilder();
    }

    return renderTemplateGallery();
  };

  return (
    <div>
      {renderContent()}
    </div>
  );
};

export default WorkflowBuilder;
