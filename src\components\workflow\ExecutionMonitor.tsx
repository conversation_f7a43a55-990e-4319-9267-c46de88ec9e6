import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Play, 
  Pause, 
  Square, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  Activity,
  Zap
} from 'lucide-react';
import { ExecutionEvent, ExecutionProgress } from '@/types/workflow';
import { executionWebSocketService } from '@/lib/websocket/ExecutionWebSocketService';
import { workflowEngine } from '@/lib/execution/engine';

interface ExecutionMonitorProps {
  executionId: string;
  workflowId: string;
  workflowName: string;
  onClose?: () => void;
}

interface ExecutionLog {
  id: string;
  timestamp: Date;
  type: 'info' | 'success' | 'error' | 'warning';
  message: string;
  nodeId?: string;
  nodeName?: string;
  data?: any;
}

export const ExecutionMonitor: React.FC<ExecutionMonitorProps> = ({
  executionId,
  workflowId,
  workflowName,
  onClose,
}) => {
  const [progress, setProgress] = useState<ExecutionProgress | null>(null);
  const [logs, setLogs] = useState<ExecutionLog[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('disconnected');
  const unsubscribeRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    // Connect to WebSocket and subscribe to execution events
    const connectAndSubscribe = async () => {
      try {
        setConnectionStatus('connecting');
        await executionWebSocketService.connect();
        setConnectionStatus('connected');
        setIsConnected(true);

        // Subscribe to execution events
        const unsubscribe = executionWebSocketService.subscribeToExecution(
          executionId,
          workflowId,
          'current-user', // In real app, get from auth context
          handleExecutionEvent
        );

        unsubscribeRef.current = unsubscribe;

        // Get initial progress
        const initialProgress = await workflowEngine.getExecutionProgress(executionId);
        if (initialProgress) {
          setProgress(initialProgress);
        }

      } catch (error) {
        console.error('Failed to connect to execution monitoring:', error);
        setConnectionStatus('disconnected');
        setIsConnected(false);
      }
    };

    connectAndSubscribe();

    // Cleanup on unmount
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
    };
  }, [executionId, workflowId]);

  const handleExecutionEvent = (event: ExecutionEvent) => {
    // Update progress for progress events
    if (event.type === 'execution_progress') {
      setProgress(prev => ({
        ...prev,
        ...event.data,
        executionId,
      }));
    }

    // Add log entry
    const logEntry: ExecutionLog = {
      id: `${event.timestamp.getTime()}-${Math.random()}`,
      timestamp: event.timestamp,
      type: getLogType(event.type),
      message: getLogMessage(event),
      nodeId: event.nodeId,
      nodeName: event.data?.node?.name,
      data: event.data,
    };

    setLogs(prev => [logEntry, ...prev].slice(0, 100)); // Keep last 100 logs
  };

  const getLogType = (eventType: string): 'info' | 'success' | 'error' | 'warning' => {
    switch (eventType) {
      case 'workflow_completed':
      case 'node_completed':
        return 'success';
      case 'workflow_failed':
      case 'node_failed':
        return 'error';
      case 'workflow_started':
      case 'node_started':
        return 'info';
      default:
        return 'info';
    }
  };

  const getLogMessage = (event: ExecutionEvent): string => {
    switch (event.type) {
      case 'workflow_started':
        return `Workflow "${event.data?.workflow?.name || workflowName}" started`;
      case 'workflow_completed':
        return `Workflow completed successfully in ${event.data?.duration || 0}ms`;
      case 'workflow_failed':
        return `Workflow failed: ${event.data?.error?.message || 'Unknown error'}`;
      case 'node_started':
        return `Node "${event.data?.node?.name || event.nodeId}" started`;
      case 'node_completed':
        return `Node "${event.data?.node?.name || event.nodeId}" completed`;
      case 'node_failed':
        return `Node "${event.data?.node?.name || event.nodeId}" failed: ${event.data?.result?.error?.message || 'Unknown error'}`;
      case 'execution_progress':
        return `Progress: ${event.data?.completedNodes || 0}/${event.data?.totalNodes || 0} nodes completed`;
      default:
        return `${event.type}: ${JSON.stringify(event.data)}`;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Activity className="h-4 w-4 text-blue-500 animate-pulse" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'bg-blue-500';
      case 'success':
        return 'bg-green-500';
      case 'error':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const formatDuration = (ms: number): string => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  const formatTime = (date: Date): string => {
    return date.toLocaleTimeString();
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <Zap className="h-5 w-5" />
              <span>Execution Monitor</span>
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Badge variant={isConnected ? 'default' : 'secondary'}>
                {connectionStatus}
              </Badge>
              {onClose && (
                <Button variant="outline" size="sm" onClick={onClose}>
                  Close
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Execution Info */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-500">Workflow:</span>
              <div className="font-medium">{workflowName}</div>
            </div>
            <div>
              <span className="text-gray-500">Execution ID:</span>
              <div className="font-mono text-xs">{executionId}</div>
            </div>
          </div>

          {/* Progress */}
          {progress && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {getStatusIcon(progress.status)}
                  <span className="font-medium capitalize">{progress.status}</span>
                </div>
                <div className="text-sm text-gray-500">
                  {progress.completedNodes}/{progress.totalNodes} nodes
                </div>
              </div>
              
              <Progress 
                value={progress.percentage} 
                className="h-2"
              />
              
              <div className="flex justify-between text-xs text-gray-500">
                <span>Duration: {formatDuration(progress.duration)}</span>
                {progress.estimatedTimeRemaining && (
                  <span>ETA: {formatDuration(progress.estimatedTimeRemaining)}</span>
                )}
              </div>
              
              {progress.currentNode && (
                <div className="text-sm">
                  <span className="text-gray-500">Current node:</span>
                  <span className="ml-1 font-medium">{progress.currentNode}</span>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Execution Logs */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm">Execution Logs</CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-64">
            <div className="space-y-2">
              {logs.length === 0 ? (
                <div className="text-center text-gray-500 py-8">
                  No logs yet...
                </div>
              ) : (
                logs.map((log) => (
                  <div
                    key={log.id}
                    className="flex items-start space-x-2 p-2 rounded border-l-2"
                    style={{ borderLeftColor: getStatusColor(log.type).replace('bg-', '') }}
                  >
                    <div className="text-xs text-gray-500 mt-0.5 min-w-[60px]">
                      {formatTime(log.timestamp)}
                    </div>
                    <div className="flex-1">
                      <div className="text-sm">{log.message}</div>
                      {log.nodeName && (
                        <div className="text-xs text-gray-500">
                          Node: {log.nodeName}
                        </div>
                      )}
                    </div>
                    <Badge 
                      variant={log.type === 'error' ? 'destructive' : 'secondary'}
                      className="text-xs"
                    >
                      {log.type}
                    </Badge>
                  </div>
                ))
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
};
