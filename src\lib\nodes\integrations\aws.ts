import { EnhancedNodeType } from '@/types/workflow';

export const awsNodes: EnhancedNodeType[] = [
  {
    name: 'AWS S3 Upload File',
    type: 'action',
    category: 'Cloud Storage',
    description: 'Upload a file to Amazon S3',
    version: '1.0.0',
    icon: '☁️',
    color: '#FF9900',
    tags: ['aws', 's3', 'storage', 'upload'],
    inputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Input',
        required: false,
      },
    ],
    outputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Output',
      },
    ],
    properties: [
      {
        name: 'credential',
        displayName: 'Credential',
        type: 'credential',
        required: true,
        description: 'AWS credentials',
      },
      {
        name: 'bucket',
        displayName: 'Bucket Name',
        type: 'string',
        required: true,
        placeholder: 'my-bucket',
        description: 'Name of the S3 bucket',
      },
      {
        name: 'key',
        displayName: 'Object Key',
        type: 'string',
        required: true,
        placeholder: 'folder/file.txt',
        description: 'Key (path) for the object in S3',
      },
      {
        name: 'file',
        displayName: 'File',
        type: 'file',
        required: true,
        description: 'File to upload',
      },
      {
        name: 'contentType',
        displayName: 'Content Type',
        type: 'string',
        required: false,
        placeholder: 'text/plain',
        description: 'MIME type of the file (auto-detected if not specified)',
      },
      {
        name: 'acl',
        displayName: 'ACL',
        type: 'select',
        required: false,
        default: 'private',
        options: [
          { value: 'private', label: 'Private' },
          { value: 'public-read', label: 'Public Read' },
          { value: 'public-read-write', label: 'Public Read Write' },
          { value: 'authenticated-read', label: 'Authenticated Read' },
        ],
        description: 'Access control list for the object',
      },
      {
        name: 'metadata',
        displayName: 'Metadata',
        type: 'json',
        required: false,
        description: 'Custom metadata for the object (JSON format)',
        default: {},
      },
      {
        name: 'tags',
        displayName: 'Tags',
        type: 'json',
        required: false,
        description: 'Tags for the object (JSON format)',
        default: {},
      },
    ],
    credentials: [
      {
        name: 'aws',
        required: true,
      },
    ],
    documentation: {
      description: 'Upload files to Amazon S3 with customizable permissions and metadata.',
      examples: [
        {
          title: 'Simple Upload',
          description: 'Upload a file with default settings',
          configuration: {
            bucket: 'my-documents',
            key: 'reports/monthly-report.pdf',
            file: '{{$input.file}}',
          },
        },
        {
          title: 'Public Upload with Metadata',
          description: 'Upload a file with public access and custom metadata',
          configuration: {
            bucket: 'public-assets',
            key: 'images/logo.png',
            file: '{{$input.file}}',
            acl: 'public-read',
            metadata: {
              'uploaded-by': 'workflow',
              'purpose': 'website-logo',
            },
            tags: {
              'environment': 'production',
              'type': 'image',
            },
          },
        },
      ],
    },
  },
  {
    name: 'AWS S3 Download File',
    type: 'action',
    category: 'Cloud Storage',
    description: 'Download a file from Amazon S3',
    version: '1.0.0',
    icon: '⬇️',
    color: '#FF9900',
    tags: ['aws', 's3', 'storage', 'download'],
    inputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Input',
        required: false,
      },
    ],
    outputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Output',
      },
    ],
    properties: [
      {
        name: 'credential',
        displayName: 'Credential',
        type: 'credential',
        required: true,
        description: 'AWS credentials',
      },
      {
        name: 'bucket',
        displayName: 'Bucket Name',
        type: 'string',
        required: true,
        placeholder: 'my-bucket',
        description: 'Name of the S3 bucket',
      },
      {
        name: 'key',
        displayName: 'Object Key',
        type: 'string',
        required: true,
        placeholder: 'folder/file.txt',
        description: 'Key (path) of the object in S3',
      },
      {
        name: 'outputFormat',
        displayName: 'Output Format',
        type: 'select',
        required: false,
        default: 'buffer',
        options: [
          { value: 'buffer', label: 'Buffer' },
          { value: 'base64', label: 'Base64 String' },
          { value: 'stream', label: 'Stream' },
        ],
        description: 'Format for the downloaded file data',
      },
    ],
    credentials: [
      {
        name: 'aws',
        required: true,
      },
    ],
  },
  {
    name: 'AWS Lambda Invoke',
    type: 'action',
    category: 'Compute',
    description: 'Invoke an AWS Lambda function',
    version: '1.0.0',
    icon: 'λ',
    color: '#FF9900',
    tags: ['aws', 'lambda', 'serverless', 'compute'],
    inputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Input',
        required: false,
      },
    ],
    outputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Output',
      },
    ],
    properties: [
      {
        name: 'credential',
        displayName: 'Credential',
        type: 'credential',
        required: true,
        description: 'AWS credentials',
      },
      {
        name: 'functionName',
        displayName: 'Function Name',
        type: 'string',
        required: true,
        placeholder: 'my-function',
        description: 'Name of the Lambda function',
      },
      {
        name: 'invocationType',
        displayName: 'Invocation Type',
        type: 'select',
        required: false,
        default: 'RequestResponse',
        options: [
          { value: 'RequestResponse', label: 'Synchronous' },
          { value: 'Event', label: 'Asynchronous' },
          { value: 'DryRun', label: 'Dry Run' },
        ],
        description: 'How to invoke the function',
      },
      {
        name: 'payload',
        displayName: 'Payload',
        type: 'json',
        required: false,
        description: 'JSON payload to send to the function',
        default: {},
      },
      {
        name: 'qualifier',
        displayName: 'Qualifier',
        type: 'string',
        required: false,
        placeholder: '$LATEST',
        description: 'Function version or alias',
      },
    ],
    credentials: [
      {
        name: 'aws',
        required: true,
      },
    ],
  },
  {
    name: 'AWS SES Send Email',
    type: 'action',
    category: 'Communication',
    description: 'Send an email using Amazon SES',
    version: '1.0.0',
    icon: '📧',
    color: '#FF9900',
    tags: ['aws', 'ses', 'email', 'communication'],
    inputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Input',
        required: false,
      },
    ],
    outputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Output',
      },
    ],
    properties: [
      {
        name: 'credential',
        displayName: 'Credential',
        type: 'credential',
        required: true,
        description: 'AWS credentials',
      },
      {
        name: 'from',
        displayName: 'From',
        type: 'string',
        required: true,
        placeholder: '<EMAIL>',
        description: 'Sender email address (must be verified in SES)',
      },
      {
        name: 'to',
        displayName: 'To',
        type: 'string',
        required: true,
        placeholder: '<EMAIL>',
        description: 'Recipient email address(es), comma-separated',
      },
      {
        name: 'cc',
        displayName: 'CC',
        type: 'string',
        required: false,
        placeholder: '<EMAIL>',
        description: 'CC email address(es), comma-separated',
      },
      {
        name: 'bcc',
        displayName: 'BCC',
        type: 'string',
        required: false,
        placeholder: '<EMAIL>',
        description: 'BCC email address(es), comma-separated',
      },
      {
        name: 'subject',
        displayName: 'Subject',
        type: 'string',
        required: true,
        placeholder: 'Email Subject',
        description: 'Email subject line',
      },
      {
        name: 'bodyText',
        displayName: 'Body (Text)',
        type: 'string',
        required: false,
        placeholder: 'Plain text email body...',
        description: 'Plain text version of the email body',
        rows: 5,
      },
      {
        name: 'bodyHtml',
        displayName: 'Body (HTML)',
        type: 'string',
        required: false,
        placeholder: '<h1>HTML email body...</h1>',
        description: 'HTML version of the email body',
        rows: 5,
      },
      {
        name: 'replyTo',
        displayName: 'Reply To',
        type: 'string',
        required: false,
        placeholder: '<EMAIL>',
        description: 'Reply-to email address',
      },
    ],
    credentials: [
      {
        name: 'aws',
        required: true,
      },
    ],
  },
];
