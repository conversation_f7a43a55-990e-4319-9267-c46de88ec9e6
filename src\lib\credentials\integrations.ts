import { CredentialType } from '@/types/workflow';

export const integrationCredentials: CredentialType[] = [
  {
    name: 'slackApi',
    displayName: 'Slack API',
    description: 'Slack Bot Token for API access',
    icon: '💬',
    color: '#4A154B',
    properties: [
      {
        name: 'token',
        displayName: 'Bot User OAuth Token',
        type: 'password',
        required: true,
        placeholder: 'xoxb-your-token-here',
        description: 'Slack Bot User OAuth Token (starts with xoxb-)',
        validation: [
          {
            type: 'pattern',
            value: '^xoxb-',
            message: 'Token must start with xoxb-',
          },
        ],
      },
      {
        name: 'signingSecret',
        displayName: 'Signing Secret',
        type: 'password',
        required: false,
        placeholder: 'your-signing-secret',
        description: 'Slack App Signing Secret (for webhook verification)',
      },
    ],
    documentation: {
      description: 'To get your Slack API credentials, create a Slack app at https://api.slack.com/apps',
      steps: [
        'Go to https://api.slack.com/apps and click "Create New App"',
        'Choose "From scratch" and enter your app name and workspace',
        'Go to "OAuth & Permissions" in the sidebar',
        'Add the required bot token scopes (chat:write, channels:read, etc.)',
        'Install the app to your workspace',
        'Copy the "Bot User OAuth Token" from the OAuth & Permissions page',
      ],
    },
  },
  {
    name: 'githubApi',
    displayName: 'GitHub API',
    description: 'GitHub Personal Access Token or GitHub App credentials',
    icon: '🐙',
    color: '#24292e',
    properties: [
      {
        name: 'authType',
        displayName: 'Authentication Type',
        type: 'select',
        required: true,
        default: 'token',
        options: [
          { value: 'token', label: 'Personal Access Token' },
          { value: 'app', label: 'GitHub App' },
        ],
        description: 'Type of GitHub authentication',
      },
      {
        name: 'token',
        displayName: 'Personal Access Token',
        type: 'password',
        required: true,
        placeholder: 'ghp_xxxxxxxxxxxxxxxxxxxx',
        description: 'GitHub Personal Access Token',
        showIf: { authType: 'token' },
        validation: [
          {
            type: 'pattern',
            value: '^gh[ps]_',
            message: 'Token must start with ghp_ or ghs_',
          },
        ],
      },
      {
        name: 'appId',
        displayName: 'App ID',
        type: 'string',
        required: true,
        placeholder: '123456',
        description: 'GitHub App ID',
        showIf: { authType: 'app' },
      },
      {
        name: 'privateKey',
        displayName: 'Private Key',
        type: 'password',
        required: true,
        placeholder: '-----BEGIN RSA PRIVATE KEY-----',
        description: 'GitHub App Private Key (PEM format)',
        showIf: { authType: 'app' },
        rows: 10,
      },
      {
        name: 'installationId',
        displayName: 'Installation ID',
        type: 'string',
        required: false,
        placeholder: '12345678',
        description: 'GitHub App Installation ID (optional)',
        showIf: { authType: 'app' },
      },
    ],
    documentation: {
      description: 'GitHub API credentials for accessing repositories, issues, and other GitHub resources.',
      steps: [
        'For Personal Access Token: Go to GitHub Settings > Developer settings > Personal access tokens',
        'Click "Generate new token" and select the required scopes',
        'For GitHub App: Go to GitHub Settings > Developer settings > GitHub Apps',
        'Create a new GitHub App and generate a private key',
        'Install the app to your repositories and note the installation ID',
      ],
    },
  },
  {
    name: 'aws',
    displayName: 'Amazon Web Services',
    description: 'AWS Access Key and Secret for API access',
    icon: '☁️',
    color: '#FF9900',
    properties: [
      {
        name: 'accessKeyId',
        displayName: 'Access Key ID',
        type: 'string',
        required: true,
        placeholder: 'AKIAIOSFODNN7EXAMPLE',
        description: 'AWS Access Key ID',
        validation: [
          {
            type: 'pattern',
            value: '^AKIA[0-9A-Z]{16}$',
            message: 'Access Key ID must start with AKIA and be 20 characters long',
          },
        ],
      },
      {
        name: 'secretAccessKey',
        displayName: 'Secret Access Key',
        type: 'password',
        required: true,
        placeholder: 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
        description: 'AWS Secret Access Key',
      },
      {
        name: 'region',
        displayName: 'Default Region',
        type: 'select',
        required: true,
        default: 'us-east-1',
        options: [
          { value: 'us-east-1', label: 'US East (N. Virginia)' },
          { value: 'us-east-2', label: 'US East (Ohio)' },
          { value: 'us-west-1', label: 'US West (N. California)' },
          { value: 'us-west-2', label: 'US West (Oregon)' },
          { value: 'eu-west-1', label: 'Europe (Ireland)' },
          { value: 'eu-west-2', label: 'Europe (London)' },
          { value: 'eu-central-1', label: 'Europe (Frankfurt)' },
          { value: 'ap-southeast-1', label: 'Asia Pacific (Singapore)' },
          { value: 'ap-southeast-2', label: 'Asia Pacific (Sydney)' },
          { value: 'ap-northeast-1', label: 'Asia Pacific (Tokyo)' },
        ],
        description: 'Default AWS region for API calls',
      },
      {
        name: 'sessionToken',
        displayName: 'Session Token',
        type: 'password',
        required: false,
        placeholder: 'Optional session token for temporary credentials',
        description: 'AWS Session Token (for temporary credentials)',
      },
    ],
    documentation: {
      description: 'AWS credentials for accessing Amazon Web Services APIs.',
      steps: [
        'Go to AWS IAM Console > Users',
        'Create a new user or select an existing user',
        'Attach the necessary policies for the services you want to use',
        'Go to Security credentials tab and create an access key',
        'Copy the Access Key ID and Secret Access Key',
        'Choose the appropriate AWS region for your resources',
      ],
    },
  },
  {
    name: 'googleApi',
    displayName: 'Google API',
    description: 'Google Service Account or OAuth2 credentials',
    icon: '🔍',
    color: '#4285F4',
    properties: [
      {
        name: 'authType',
        displayName: 'Authentication Type',
        type: 'select',
        required: true,
        default: 'serviceAccount',
        options: [
          { value: 'serviceAccount', label: 'Service Account' },
          { value: 'oauth2', label: 'OAuth2' },
        ],
        description: 'Type of Google authentication',
      },
      {
        name: 'serviceAccountKey',
        displayName: 'Service Account Key',
        type: 'json',
        required: true,
        placeholder: '{"type": "service_account", "project_id": "..."}',
        description: 'Google Service Account Key (JSON format)',
        showIf: { authType: 'serviceAccount' },
        rows: 10,
      },
      {
        name: 'clientId',
        displayName: 'Client ID',
        type: 'string',
        required: true,
        placeholder: 'your-client-id.googleusercontent.com',
        description: 'Google OAuth2 Client ID',
        showIf: { authType: 'oauth2' },
      },
      {
        name: 'clientSecret',
        displayName: 'Client Secret',
        type: 'password',
        required: true,
        placeholder: 'your-client-secret',
        description: 'Google OAuth2 Client Secret',
        showIf: { authType: 'oauth2' },
      },
      {
        name: 'refreshToken',
        displayName: 'Refresh Token',
        type: 'password',
        required: true,
        placeholder: 'your-refresh-token',
        description: 'Google OAuth2 Refresh Token',
        showIf: { authType: 'oauth2' },
      },
      {
        name: 'scopes',
        displayName: 'Scopes',
        type: 'string',
        required: false,
        placeholder: 'https://www.googleapis.com/auth/spreadsheets',
        description: 'Comma-separated list of OAuth2 scopes',
        showIf: { authType: 'oauth2' },
      },
    ],
    documentation: {
      description: 'Google API credentials for accessing Google Workspace and other Google services.',
      steps: [
        'Go to Google Cloud Console > APIs & Services > Credentials',
        'For Service Account: Create a service account and download the JSON key file',
        'For OAuth2: Create OAuth2 credentials and configure authorized redirect URIs',
        'Enable the required APIs (Sheets, Drive, Gmail, etc.) in the API Library',
        'For OAuth2: Use the OAuth2 playground to get refresh tokens',
      ],
    },
  },
];
