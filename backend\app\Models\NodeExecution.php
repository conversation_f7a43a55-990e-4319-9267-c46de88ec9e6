<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class NodeExecution extends Model
{
    protected $fillable = [
        'workflow_execution_id',
        'node_id',
        'node_type',
        'node_name',
        'status',
        'started_at',
        'finished_at',
        'duration',
        'input_data',
        'output_data',
        'error_message',
        'retry_count',
        'metadata',
    ];

    protected $casts = [
        'started_at' => 'datetime',
        'finished_at' => 'datetime',
        'duration' => 'integer',
        'input_data' => 'array',
        'output_data' => 'array',
        'retry_count' => 'integer',
        'metadata' => 'array',
    ];

    public function workflowExecution(): BelongsTo
    {
        return $this->belongsTo(WorkflowExecution::class);
    }

    // Scope for successful node executions
    public function scopeSuccessful($query)
    {
        return $query->where('status', 'success');
    }

    // Scope for failed node executions
    public function scopeFailed($query)
    {
        return $query->where('status', 'error');
    }

    // Check if node execution is successful
    public function isSuccessful(): bool
    {
        return $this->status === 'success';
    }

    // Check if node execution failed
    public function isFailed(): bool
    {
        return $this->status === 'error';
    }

    // Get execution duration in seconds
    public function getDurationInSeconds(): ?float
    {
        return $this->duration ? $this->duration / 1000 : null;
    }

    // Mark node execution as started
    public function markAsStarted(): void
    {
        $this->update([
            'status' => 'running',
            'started_at' => now(),
        ]);
    }

    // Mark node execution as completed
    public function markAsCompleted(array $outputData = null): void
    {
        $this->update([
            'status' => 'success',
            'finished_at' => now(),
            'duration' => $this->started_at ? now()->diffInMilliseconds($this->started_at) : 0,
            'output_data' => $outputData,
        ]);
    }

    // Mark node execution as failed
    public function markAsFailed(string $errorMessage): void
    {
        $this->update([
            'status' => 'error',
            'finished_at' => now(),
            'duration' => $this->started_at ? now()->diffInMilliseconds($this->started_at) : 0,
            'error_message' => $errorMessage,
        ]);
    }

    // Increment retry count
    public function incrementRetryCount(): void
    {
        $this->increment('retry_count');
    }
}
