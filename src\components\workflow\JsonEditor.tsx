import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, Check, Eye, Edit } from 'lucide-react';

interface JsonEditorProps {
  value: any;
  onChange: (value: any) => void;
  height?: number;
  hasError?: boolean;
  readOnly?: boolean;
}

export const JsonEditor: React.FC<JsonEditorProps> = ({
  value,
  onChange,
  height = 150,
  hasError = false,
  readOnly = false,
}) => {
  const [textValue, setTextValue] = useState('');
  const [isValid, setIsValid] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'edit' | 'preview'>('edit');

  useEffect(() => {
    try {
      const formatted = value ? JSON.stringify(value, null, 2) : '{}';
      setTextValue(formatted);
      setIsValid(true);
      setError(null);
    } catch (err) {
      setTextValue(typeof value === 'string' ? value : '{}');
      setIsValid(false);
      setError('Invalid JSON format');
    }
  }, [value]);

  const handleTextChange = (newText: string) => {
    setTextValue(newText);
    
    try {
      const parsed = JSON.parse(newText || '{}');
      setIsValid(true);
      setError(null);
      onChange(parsed);
    } catch (err) {
      setIsValid(false);
      setError(err instanceof Error ? err.message : 'Invalid JSON');
    }
  };

  const formatJson = () => {
    try {
      const parsed = JSON.parse(textValue);
      const formatted = JSON.stringify(parsed, null, 2);
      setTextValue(formatted);
      setIsValid(true);
      setError(null);
      onChange(parsed);
    } catch (err) {
      // Keep current text if parsing fails
    }
  };

  const minifyJson = () => {
    try {
      const parsed = JSON.parse(textValue);
      const minified = JSON.stringify(parsed);
      setTextValue(minified);
      setIsValid(true);
      setError(null);
      onChange(parsed);
    } catch (err) {
      // Keep current text if parsing fails
    }
  };

  return (
    <Card className={`p-0 ${hasError ? 'border-red-500' : ''}`}>
      {/* Header */}
      <div className="border-b bg-gray-50 px-3 py-2 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="text-xs font-medium text-gray-600">JSON</span>
          {isValid ? (
            <Check className="h-3 w-3 text-green-500" />
          ) : (
            <AlertCircle className="h-3 w-3 text-red-500" />
          )}
        </div>
        
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setViewMode(viewMode === 'edit' ? 'preview' : 'edit')}
            className="h-6 px-2 text-xs"
          >
            {viewMode === 'edit' ? <Eye className="h-3 w-3" /> : <Edit className="h-3 w-3" />}
          </Button>
          
          {!readOnly && (
            <>
              <Button
                variant="ghost"
                size="sm"
                onClick={formatJson}
                disabled={!isValid}
                className="h-6 px-2 text-xs"
              >
                Format
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={minifyJson}
                disabled={!isValid}
                className="h-6 px-2 text-xs"
              >
                Minify
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="relative">
        {viewMode === 'edit' ? (
          <textarea
            value={textValue}
            onChange={(e) => handleTextChange(e.target.value)}
            readOnly={readOnly}
            className={`
              w-full p-3 font-mono text-sm border-0 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500
              ${hasError || !isValid ? 'border-red-500' : ''}
              ${readOnly ? 'bg-gray-50 cursor-not-allowed' : 'bg-white'}
            `}
            style={{ height: `${height}px` }}
            placeholder="Enter JSON..."
            spellCheck={false}
          />
        ) : (
          <div 
            className="p-3 font-mono text-sm bg-gray-50 overflow-auto"
            style={{ height: `${height}px` }}
          >
            <pre className="whitespace-pre-wrap text-gray-800">
              {isValid ? textValue : 'Invalid JSON'}
            </pre>
          </div>
        )}
        
        {/* Line numbers for edit mode */}
        {viewMode === 'edit' && (
          <div className="absolute left-0 top-0 p-3 text-gray-400 text-sm font-mono pointer-events-none select-none">
            {textValue.split('\n').map((_, index) => (
              <div key={index} className="leading-5">
                {index + 1}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Error display */}
      {error && (
        <div className="border-t p-2">
          <Alert variant="destructive" className="py-2">
            <AlertCircle className="h-3 w-3" />
            <AlertDescription className="text-xs">
              {error}
            </AlertDescription>
          </Alert>
        </div>
      )}

      {/* Footer */}
      <div className="border-t bg-gray-50 px-3 py-2 text-xs text-gray-600">
        <div className="flex justify-between items-center">
          <span>
            {isValid ? 'Valid JSON' : 'Invalid JSON'} • {textValue.length} characters
          </span>
          <span>
            {textValue.split('\n').length} lines
          </span>
        </div>
      </div>
    </Card>
  );
};
