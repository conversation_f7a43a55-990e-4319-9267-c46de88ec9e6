// Enhanced Node Registry
// This file contains the registry of all available node types

import { EnhancedNodeType, NodeProperty } from '@/types/workflow';

// Node Categories
export const NODE_CATEGORIES = {
  TRIGGER: 'trigger',
  ACTION: 'action',
  LOGIC: 'logic',
  OUTPUT: 'output',
  INTEGRATION: 'integration',
} as const;

// Common property templates
const commonProperties = {
  name: {
    name: 'name',
    displayName: 'Name',
    type: 'string' as const,
    required: true,
    default: '',
    description: 'Name of the node',
    placeholder: 'Enter node name',
  },
  description: {
    name: 'description',
    displayName: 'Description',
    type: 'string' as const,
    required: false,
    default: '',
    description: 'Description of what this node does',
    placeholder: 'Enter description',
    rows: 3,
  },
};

// Enhanced Node Definitions
export const ENHANCED_NODE_TYPES: EnhancedNodeType[] = [
  // TRIGGER NODES
  {
    id: 'webhook-trigger',
    name: 'Webhook Trigger',
    type: 'trigger',
    category: 'trigger',
    subcategory: 'http',
    label: 'Webhook',
    description: 'Triggers workflow when HTTP request is received',
    icon: 'webhook',
    version: '1.0.0',
    properties: [
      commonProperties.name,
      {
        name: 'method',
        displayName: 'HTTP Method',
        type: 'select',
        required: true,
        default: 'POST',
        options: [
          { label: 'GET', value: 'GET' },
          { label: 'POST', value: 'POST' },
          { label: 'PUT', value: 'PUT' },
          { label: 'DELETE', value: 'DELETE' },
          { label: 'PATCH', value: 'PATCH' },
        ],
        description: 'HTTP method to accept',
      },
      {
        name: 'path',
        displayName: 'Webhook Path',
        type: 'string',
        required: true,
        default: '/webhook',
        description: 'URL path for the webhook',
        placeholder: '/my-webhook',
      },
      {
        name: 'authentication',
        displayName: 'Authentication',
        type: 'select',
        required: false,
        default: 'none',
        options: [
          { label: 'None', value: 'none' },
          { label: 'Basic Auth', value: 'basic' },
          { label: 'Header Token', value: 'header' },
          { label: 'Query Parameter', value: 'query' },
        ],
        description: 'Authentication method for webhook',
      },
      {
        name: 'responseMode',
        displayName: 'Response Mode',
        type: 'select',
        required: false,
        default: 'onReceived',
        options: [
          { label: 'Respond Immediately', value: 'onReceived' },
          { label: 'Respond with Last Node', value: 'lastNode' },
        ],
        description: 'When to send response to webhook caller',
      },
    ],
    inputs: [],
    outputs: [
      {
        name: 'main',
        type: 'object',
        description: 'Webhook payload and metadata',
      },
    ],
    color: '#0FA4AF',
    backgroundColor: '#0FA4AF10',
    borderColor: '#0FA4AF30',
    documentation: {
      examples: [
        {
          title: 'Simple POST webhook',
          description: 'Accept POST requests at /api/webhook',
          configuration: {
            method: 'POST',
            path: '/api/webhook',
            authentication: 'none',
          },
        },
      ],
    },
    tags: ['webhook', 'http', 'trigger'],
  },
  
  {
    id: 'schedule-trigger',
    name: 'Schedule Trigger',
    type: 'trigger',
    category: 'trigger',
    subcategory: 'time',
    label: 'Schedule',
    description: 'Triggers workflow on a schedule using cron expressions',
    icon: 'calendar',
    version: '1.0.0',
    properties: [
      commonProperties.name,
      {
        name: 'rule',
        displayName: 'Cron Expression',
        type: 'string',
        required: true,
        default: '0 9 * * 1-5',
        description: 'Cron expression for scheduling',
        placeholder: '0 9 * * 1-5 (9 AM weekdays)',
      },
      {
        name: 'timezone',
        displayName: 'Timezone',
        type: 'select',
        required: false,
        default: 'UTC',
        options: [
          { label: 'UTC', value: 'UTC' },
          { label: 'America/New_York', value: 'America/New_York' },
          { label: 'Europe/London', value: 'Europe/London' },
          { label: 'Asia/Tokyo', value: 'Asia/Tokyo' },
        ],
        description: 'Timezone for schedule execution',
      },
    ],
    inputs: [],
    outputs: [
      {
        name: 'main',
        type: 'object',
        description: 'Trigger timestamp and metadata',
      },
    ],
    color: '#964734',
    backgroundColor: '#96473410',
    borderColor: '#96473430',
    tags: ['schedule', 'cron', 'time', 'trigger'],
  },

  {
    id: 'manual-trigger',
    name: 'Manual Trigger',
    type: 'trigger',
    category: 'trigger',
    subcategory: 'manual',
    label: 'Manual',
    description: 'Manually trigger workflow execution',
    icon: 'mouse-pointer',
    version: '1.0.0',
    properties: [
      commonProperties.name,
      {
        name: 'inputData',
        displayName: 'Input Data',
        type: 'json',
        required: false,
        default: '{}',
        description: 'JSON data to pass to workflow',
        rows: 5,
      },
    ],
    inputs: [],
    outputs: [
      {
        name: 'main',
        type: 'object',
        description: 'Manual trigger data',
      },
    ],
    color: '#024950',
    backgroundColor: '#02495010',
    borderColor: '#02495030',
    tags: ['manual', 'trigger'],
  },

  // ACTION NODES
  {
    id: 'http-request',
    name: 'HTTP Request',
    type: 'action',
    category: 'action',
    subcategory: 'http',
    label: 'HTTP Request',
    description: 'Make HTTP requests to external APIs',
    icon: 'globe',
    version: '1.0.0',
    properties: [
      commonProperties.name,
      {
        name: 'method',
        displayName: 'Method',
        type: 'select',
        required: true,
        default: 'GET',
        options: [
          { label: 'GET', value: 'GET' },
          { label: 'POST', value: 'POST' },
          { label: 'PUT', value: 'PUT' },
          { label: 'DELETE', value: 'DELETE' },
          { label: 'PATCH', value: 'PATCH' },
        ],
        description: 'HTTP method to use',
      },
      {
        name: 'url',
        displayName: 'URL',
        type: 'string',
        required: true,
        default: '',
        description: 'URL to make request to',
        placeholder: 'https://api.example.com/data',
      },
      {
        name: 'headers',
        displayName: 'Headers',
        type: 'json',
        required: false,
        default: '{}',
        description: 'HTTP headers to send',
        rows: 3,
      },
      {
        name: 'body',
        displayName: 'Body',
        type: 'json',
        required: false,
        default: '{}',
        description: 'Request body (for POST/PUT/PATCH)',
        rows: 5,
        condition: (values) => ['POST', 'PUT', 'PATCH'].includes(values.method),
      },
    ],
    inputs: [
      {
        name: 'main',
        type: 'any',
        required: false,
        description: 'Input data from previous node',
      },
    ],
    outputs: [
      {
        name: 'main',
        type: 'object',
        description: 'HTTP response data',
      },
    ],
    retryPolicy: {
      enabled: true,
      maxAttempts: 3,
      delay: 1000,
      backoffMultiplier: 2,
    },
    timeout: 30000,
    color: '#0FA4AF',
    backgroundColor: '#0FA4AF10',
    borderColor: '#0FA4AF30',
    tags: ['http', 'api', 'request'],
  },
];

// Node Registry Class
export class NodeRegistry {
  private static instance: NodeRegistry;
  private nodes: Map<string, EnhancedNodeType> = new Map();

  private constructor() {
    this.registerBuiltInNodes();
  }

  public static getInstance(): NodeRegistry {
    if (!NodeRegistry.instance) {
      NodeRegistry.instance = new NodeRegistry();
    }
    return NodeRegistry.instance;
  }

  private registerBuiltInNodes(): void {
    ENHANCED_NODE_TYPES.forEach(node => {
      this.nodes.set(node.id, node);
    });
  }

  public registerNode(node: EnhancedNodeType): void {
    this.nodes.set(node.id, node);
  }

  public getNode(id: string): EnhancedNodeType | undefined {
    return this.nodes.get(id);
  }

  public getAllNodes(): EnhancedNodeType[] {
    return Array.from(this.nodes.values());
  }

  public getNodesByCategory(category: string): EnhancedNodeType[] {
    return this.getAllNodes().filter(node => node.category === category);
  }

  public getNodesByType(type: string): EnhancedNodeType[] {
    return this.getAllNodes().filter(node => node.type === type);
  }

  public searchNodes(query: string): EnhancedNodeType[] {
    const lowerQuery = query.toLowerCase();
    return this.getAllNodes().filter(node => 
      node.name.toLowerCase().includes(lowerQuery) ||
      node.description.toLowerCase().includes(lowerQuery) ||
      node.tags?.some(tag => tag.toLowerCase().includes(lowerQuery))
    );
  }
}

// Export singleton instance
export const nodeRegistry = NodeRegistry.getInstance();
