import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { <PERSON><PERSON>, DialogContent, <PERSON>alogHeader, <PERSON>alogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Plus, Key, Edit, Trash2, TestTube } from 'lucide-react';

interface Credential {
  id: string;
  name: string;
  type: string;
  summary: Record<string, any>;
  isActive: boolean;
  lastUsed?: Date;
}

interface CredentialSelectorProps {
  value: string | null;
  credentialType: string;
  onChange: (value: string | null) => void;
  hasError?: boolean;
}

export const CredentialSelector: React.FC<CredentialSelectorProps> = ({
  value,
  credentialType,
  onChange,
  hasError = false,
}) => {
  const [credentials, setCredentials] = useState<Credential[]>([]);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newCredential, setNewCredential] = useState({
    name: '',
    type: credentialType,
    data: {} as Record<string, any>,
  });

  // Mock credentials data - in real app, this would come from API
  useEffect(() => {
    const mockCredentials: Credential[] = [
      {
        id: '1',
        name: 'Production API Key',
        type: 'http_header',
        summary: { header_name: 'Authorization' },
        isActive: true,
        lastUsed: new Date('2024-01-10'),
      },
      {
        id: '2',
        name: 'Database Connection',
        type: 'database',
        summary: { host: 'db.example.com', database: 'production', username: 'api_user' },
        isActive: true,
      },
      {
        id: '3',
        name: 'Email SMTP',
        type: 'email',
        summary: { host: 'smtp.gmail.com', username: '<EMAIL>' },
        isActive: true,
      },
    ];
    
    setCredentials(mockCredentials.filter(c => c.type === credentialType));
  }, [credentialType]);

  const getCredentialTypeFields = (type: string) => {
    switch (type) {
      case 'http_basic':
        return [
          { name: 'username', label: 'Username', type: 'text', required: true },
          { name: 'password', label: 'Password', type: 'password', required: true },
        ];
      case 'http_header':
        return [
          { name: 'header_name', label: 'Header Name', type: 'text', required: true, placeholder: 'Authorization' },
          { name: 'header_value', label: 'Header Value', type: 'password', required: true, placeholder: 'Bearer token...' },
        ];
      case 'oauth2':
        return [
          { name: 'client_id', label: 'Client ID', type: 'text', required: true },
          { name: 'client_secret', label: 'Client Secret', type: 'password', required: true },
          { name: 'scope', label: 'Scope', type: 'text', required: false },
          { name: 'auth_url', label: 'Authorization URL', type: 'url', required: true },
          { name: 'token_url', label: 'Token URL', type: 'url', required: true },
        ];
      case 'database':
        return [
          { name: 'host', label: 'Host', type: 'text', required: true },
          { name: 'port', label: 'Port', type: 'number', required: false, placeholder: '5432' },
          { name: 'database', label: 'Database', type: 'text', required: true },
          { name: 'username', label: 'Username', type: 'text', required: true },
          { name: 'password', label: 'Password', type: 'password', required: true },
        ];
      case 'email':
        return [
          { name: 'host', label: 'SMTP Host', type: 'text', required: true },
          { name: 'port', label: 'Port', type: 'number', required: false, placeholder: '587' },
          { name: 'username', label: 'Username', type: 'text', required: true },
          { name: 'password', label: 'Password', type: 'password', required: true },
          { name: 'secure', label: 'Use TLS', type: 'checkbox', required: false },
        ];
      default:
        return [
          { name: 'value', label: 'Value', type: 'password', required: true },
        ];
    }
  };

  const handleCreateCredential = async () => {
    // In real app, this would make API call
    const newCred: Credential = {
      id: Date.now().toString(),
      name: newCredential.name,
      type: newCredential.type,
      summary: getSummaryFromData(newCredential.data),
      isActive: true,
    };
    
    setCredentials([...credentials, newCred]);
    onChange(newCred.id);
    setIsCreateDialogOpen(false);
    setNewCredential({ name: '', type: credentialType, data: {} });
  };

  const getSummaryFromData = (data: Record<string, any>) => {
    const summary: Record<string, any> = {};
    
    // Extract non-sensitive fields for display
    Object.keys(data).forEach(key => {
      if (!key.toLowerCase().includes('password') && 
          !key.toLowerCase().includes('secret') && 
          !key.toLowerCase().includes('token')) {
        summary[key] = data[key];
      }
    });
    
    return summary;
  };

  const testCredential = async (credentialId: string) => {
    // Mock credential test
    console.log('Testing credential:', credentialId);
    // In real app, this would make API call to test the credential
  };

  const formatCredentialDisplay = (credential: Credential) => {
    const summaryText = Object.entries(credential.summary)
      .map(([key, value]) => `${key}: ${value}`)
      .join(', ');
    
    return summaryText || credential.name;
  };

  return (
    <div className="space-y-2">
      <div className="flex space-x-2">
        <Select value={value || ''} onValueChange={onChange}>
          <SelectTrigger className={hasError ? 'border-red-500' : ''}>
            <SelectValue placeholder="Select a credential" />
          </SelectTrigger>
          <SelectContent>
            {credentials.map((credential) => (
              <SelectItem key={credential.id} value={credential.id}>
                <div className="flex items-center justify-between w-full">
                  <div>
                    <div className="font-medium">{credential.name}</div>
                    <div className="text-xs text-gray-500">
                      {formatCredentialDisplay(credential)}
                    </div>
                  </div>
                  <div className="flex items-center space-x-1 ml-2">
                    {credential.isActive ? (
                      <Badge variant="outline" className="text-xs">Active</Badge>
                    ) : (
                      <Badge variant="secondary" className="text-xs">Inactive</Badge>
                    )}
                  </div>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm">
              <Plus className="h-4 w-4" />
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Create New Credential</DialogTitle>
            </DialogHeader>
            
            <div className="space-y-4">
              <div>
                <Label htmlFor="credential-name">Name</Label>
                <Input
                  id="credential-name"
                  value={newCredential.name}
                  onChange={(e) => setNewCredential({ ...newCredential, name: e.target.value })}
                  placeholder="Enter credential name"
                />
              </div>

              <div>
                <Label>Type</Label>
                <div className="mt-1">
                  <Badge variant="outline">{credentialType}</Badge>
                </div>
              </div>

              {getCredentialTypeFields(credentialType).map((field) => (
                <div key={field.name}>
                  <Label htmlFor={field.name}>{field.label}</Label>
                  {field.type === 'checkbox' ? (
                    <div className="flex items-center space-x-2 mt-1">
                      <input
                        type="checkbox"
                        id={field.name}
                        checked={!!newCredential.data[field.name]}
                        onChange={(e) => setNewCredential({
                          ...newCredential,
                          data: { ...newCredential.data, [field.name]: e.target.checked }
                        })}
                      />
                      <Label htmlFor={field.name} className="text-sm">{field.label}</Label>
                    </div>
                  ) : (
                    <Input
                      id={field.name}
                      type={field.type}
                      value={newCredential.data[field.name] || ''}
                      onChange={(e) => setNewCredential({
                        ...newCredential,
                        data: { ...newCredential.data, [field.name]: e.target.value }
                      })}
                      placeholder={field.placeholder}
                      required={field.required}
                    />
                  )}
                </div>
              ))}

              <div className="flex space-x-2 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setIsCreateDialogOpen(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateCredential}
                  disabled={!newCredential.name}
                  className="flex-1"
                >
                  Create
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Selected credential info */}
      {value && (
        <Card className="p-3">
          {(() => {
            const selectedCredential = credentials.find(c => c.id === value);
            if (!selectedCredential) return null;
            
            return (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Key className="h-4 w-4 text-gray-500" />
                    <span className="font-medium text-sm">{selectedCredential.name}</span>
                    <Badge variant="outline" className="text-xs">{selectedCredential.type}</Badge>
                  </div>
                  
                  <div className="flex space-x-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => testCredential(selectedCredential.id)}
                      className="h-6 px-2"
                    >
                      <TestTube className="h-3 w-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 px-2"
                    >
                      <Edit className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
                
                <div className="text-xs text-gray-600">
                  {formatCredentialDisplay(selectedCredential)}
                </div>
                
                {selectedCredential.lastUsed && (
                  <div className="text-xs text-gray-500">
                    Last used: {selectedCredential.lastUsed.toLocaleDateString()}
                  </div>
                )}
              </div>
            );
          })()}
        </Card>
      )}
    </div>
  );
};
