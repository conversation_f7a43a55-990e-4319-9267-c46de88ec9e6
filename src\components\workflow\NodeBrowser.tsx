import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Search, 
  Plus, 
  Filter, 
  Star, 
  Zap,
  Grid3X3,
  List,
  ExternalLink,
  Info
} from 'lucide-react';
import { EnhancedNodeType } from '@/types/workflow';
import { integrationRegistry, integrationCategories, serviceProviders } from '@/lib/nodes/integrations';
import { nodeRegistry } from '@/lib/nodes/registry';

interface NodeBrowserProps {
  onNodeSelect: (nodeType: EnhancedNodeType) => void;
  onClose?: () => void;
}

export const NodeBrowser: React.FC<NodeBrowserProps> = ({
  onNodeSelect,
  onClose,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedProvider, setSelectedProvider] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [activeTab, setActiveTab] = useState('integrations');

  // Get all nodes based on active tab
  const allNodes = useMemo(() => {
    if (activeTab === 'integrations') {
      return integrationRegistry.getAllNodes();
    }
    return nodeRegistry.getAllNodes();
  }, [activeTab]);

  // Filter nodes based on search and filters
  const filteredNodes = useMemo(() => {
    let nodes = allNodes;

    // Apply search filter
    if (searchQuery.trim()) {
      if (activeTab === 'integrations') {
        nodes = integrationRegistry.searchNodes(searchQuery);
      } else {
        nodes = nodeRegistry.searchNodes(searchQuery);
      }
    }

    // Apply category filter
    if (selectedCategory !== 'all') {
      nodes = nodes.filter(node => node.category === selectedCategory);
    }

    // Apply provider filter (for integrations)
    if (activeTab === 'integrations' && selectedProvider !== 'all') {
      nodes = integrationRegistry.getNodesByProvider(selectedProvider);
      if (searchQuery.trim() || selectedCategory !== 'all') {
        // Re-apply other filters
        nodes = nodes.filter(node => {
          const matchesSearch = !searchQuery.trim() || 
            node.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            node.description.toLowerCase().includes(searchQuery.toLowerCase());
          const matchesCategory = selectedCategory === 'all' || node.category === selectedCategory;
          return matchesSearch && matchesCategory;
        });
      }
    }

    return nodes;
  }, [allNodes, searchQuery, selectedCategory, selectedProvider, activeTab]);

  // Get categories for current tab
  const categories = useMemo(() => {
    if (activeTab === 'integrations') {
      return Object.keys(integrationCategories);
    }
    return [...new Set(nodeRegistry.getAllNodes().map(node => node.category))];
  }, [activeTab]);

  const handleNodeAdd = (node: EnhancedNodeType) => {
    onNodeSelect(node);
    if (onClose) {
      onClose();
    }
  };

  const getNodeIcon = (node: EnhancedNodeType) => {
    return node.icon || '⚙️';
  };

  const getNodeColor = (node: EnhancedNodeType) => {
    return node.color || '#6B7280';
  };

  const getCategoryInfo = (category: string) => {
    if (activeTab === 'integrations') {
      return integrationCategories[category as keyof typeof integrationCategories];
    }
    return { name: category, description: '', icon: '⚙️', color: '#6B7280' };
  };

  const getProviderInfo = (provider: string) => {
    return serviceProviders[provider as keyof typeof serviceProviders];
  };

  const NodeCard: React.FC<{ node: EnhancedNodeType }> = ({ node }) => (
    <Card 
      className="cursor-pointer hover:shadow-md transition-shadow border-l-4"
      style={{ borderLeftColor: getNodeColor(node) }}
      onClick={() => handleNodeAdd(node)}
    >
      <CardHeader className="pb-2">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-lg">{getNodeIcon(node)}</span>
            <div>
              <CardTitle className="text-sm font-medium">{node.name}</CardTitle>
              <Badge variant="secondary" className="text-xs mt-1">
                {getCategoryInfo(node.category).name}
              </Badge>
            </div>
          </div>
          <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
            <Plus className="h-3 w-3" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <p className="text-xs text-gray-600 line-clamp-2">{node.description}</p>
        {node.tags && (
          <div className="flex flex-wrap gap-1 mt-2">
            {node.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {node.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{node.tags.length - 3}
              </Badge>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );

  const NodeListItem: React.FC<{ node: EnhancedNodeType }> = ({ node }) => (
    <div 
      className="flex items-center justify-between p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
      onClick={() => handleNodeAdd(node)}
    >
      <div className="flex items-center space-x-3">
        <span className="text-lg">{getNodeIcon(node)}</span>
        <div>
          <div className="font-medium text-sm">{node.name}</div>
          <div className="text-xs text-gray-600">{node.description}</div>
        </div>
      </div>
      <div className="flex items-center space-x-2">
        <Badge variant="secondary" className="text-xs">
          {getCategoryInfo(node.category).name}
        </Badge>
        <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
          <Plus className="h-3 w-3" />
        </Button>
      </div>
    </div>
  );

  return (
    <div className="w-full max-w-4xl mx-auto space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Zap className="h-5 w-5" />
          <h2 className="text-lg font-semibold">Node Browser</h2>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('grid')}
          >
            <Grid3X3 className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            <List className="h-4 w-4" />
          </Button>
          {onClose && (
            <Button variant="outline" size="sm" onClick={onClose}>
              Close
            </Button>
          )}
        </div>
      </div>

      {/* Search and Filters */}
      <div className="space-y-3">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search nodes..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        <div className="flex flex-wrap gap-2">
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-1 border rounded text-sm"
          >
            <option value="all">All Categories</option>
            {categories.map((category) => (
              <option key={category} value={category}>
                {getCategoryInfo(category).name}
              </option>
            ))}
          </select>

          {activeTab === 'integrations' && (
            <select
              value={selectedProvider}
              onChange={(e) => setSelectedProvider(e.target.value)}
              className="px-3 py-1 border rounded text-sm"
            >
              <option value="all">All Providers</option>
              {integrationRegistry.getProviders().map((provider) => (
                <option key={provider} value={provider}>
                  {getProviderInfo(provider)?.name || provider}
                </option>
              ))}
            </select>
          )}
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="integrations">Integrations</TabsTrigger>
          <TabsTrigger value="core">Core Nodes</TabsTrigger>
        </TabsList>

        <TabsContent value="integrations" className="space-y-4">
          <div className="text-sm text-gray-600">
            {filteredNodes.length} integration nodes available
          </div>
          
          <ScrollArea className="h-96">
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {filteredNodes.map((node) => (
                  <NodeCard key={node.name} node={node} />
                ))}
              </div>
            ) : (
              <div className="space-y-2">
                {filteredNodes.map((node) => (
                  <NodeListItem key={node.name} node={node} />
                ))}
              </div>
            )}
          </ScrollArea>
        </TabsContent>

        <TabsContent value="core" className="space-y-4">
          <div className="text-sm text-gray-600">
            {filteredNodes.length} core nodes available
          </div>
          
          <ScrollArea className="h-96">
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {filteredNodes.map((node) => (
                  <NodeCard key={node.name} node={node} />
                ))}
              </div>
            ) : (
              <div className="space-y-2">
                {filteredNodes.map((node) => (
                  <NodeListItem key={node.name} node={node} />
                ))}
              </div>
            )}
          </ScrollArea>
        </TabsContent>
      </Tabs>

      {/* Empty State */}
      {filteredNodes.length === 0 && (
        <div className="text-center py-8">
          <div className="text-gray-400 mb-2">
            <Search className="h-12 w-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-1">No nodes found</h3>
          <p className="text-gray-600">Try adjusting your search or filters</p>
        </div>
      )}
    </div>
  );
};
