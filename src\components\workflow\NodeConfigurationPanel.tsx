import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { X, Save, AlertCircle, Info } from 'lucide-react';
import { EnhancedNodeType, NodeProperty, WorkflowNode } from '@/types/workflow';
import { nodeRegistry } from '@/lib/nodes/registry';
import { CodeEditor } from './CodeEditor';
import { JsonEditor } from './JsonEditor';
import { FileUpload } from './FileUpload';
import { CredentialSelector } from './CredentialSelector';

interface NodeConfigurationPanelProps {
  node: WorkflowNode | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (nodeId: string, parameters: Record<string, any>) => void;
}

export const NodeConfigurationPanel: React.FC<NodeConfigurationPanelProps> = ({
  node,
  isOpen,
  onClose,
  onSave,
}) => {
  const [parameters, setParameters] = useState<Record<string, any>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [nodeType, setNodeType] = useState<EnhancedNodeType | null>(null);

  useEffect(() => {
    if (node) {
      setParameters(node.parameters || {});
      const type = nodeRegistry.getNode(node.type);
      setNodeType(type || null);
      setErrors({});
    }
  }, [node]);

  if (!isOpen || !node || !nodeType) {
    return null;
  }

  const handleParameterChange = (propertyName: string, value: any) => {
    const newParameters = { ...parameters, [propertyName]: value };
    setParameters(newParameters);
    
    // Clear error for this field
    if (errors[propertyName]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[propertyName];
        return newErrors;
      });
    }
  };

  const validateParameters = (): boolean => {
    const newErrors: Record<string, string> = {};

    nodeType.properties.forEach(property => {
      const value = parameters[property.name];
      
      // Check required fields
      if (property.required && (!value || value === '')) {
        newErrors[property.name] = `${property.displayName} is required`;
        return;
      }

      // Check validation rules
      if (property.validation && value) {
        property.validation.forEach(rule => {
          switch (rule.type) {
            case 'min':
              if (typeof value === 'string' && value.length < rule.value) {
                newErrors[property.name] = rule.message;
              } else if (typeof value === 'number' && value < rule.value) {
                newErrors[property.name] = rule.message;
              }
              break;
            case 'max':
              if (typeof value === 'string' && value.length > rule.value) {
                newErrors[property.name] = rule.message;
              } else if (typeof value === 'number' && value > rule.value) {
                newErrors[property.name] = rule.message;
              }
              break;
            case 'pattern':
              if (typeof value === 'string' && !new RegExp(rule.value).test(value)) {
                newErrors[property.name] = rule.message;
              }
              break;
            case 'custom':
              if (rule.validator && !rule.validator(value)) {
                newErrors[property.name] = rule.message;
              }
              break;
          }
        });
      }

      // Check conditional visibility
      if (property.condition && !property.condition(parameters)) {
        // Remove value if condition is not met
        if (parameters[property.name] !== undefined) {
          handleParameterChange(property.name, undefined);
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    if (validateParameters()) {
      onSave(node.id, parameters);
      onClose();
    }
  };

  const renderPropertyInput = (property: NodeProperty) => {
    const value = parameters[property.name] ?? property.default;
    const hasError = !!errors[property.name];

    // Check if property should be visible
    if (property.condition && !property.condition(parameters)) {
      return null;
    }

    const commonProps = {
      id: property.name,
      value: value || '',
      onChange: (newValue: any) => handleParameterChange(property.name, newValue),
      placeholder: property.placeholder,
      className: hasError ? 'border-red-500' : '',
    };

    switch (property.type) {
      case 'string':
        return (
          <Input
            {...commonProps}
            type="text"
          />
        );

      case 'number':
        return (
          <Input
            {...commonProps}
            type="number"
            onChange={(e) => handleParameterChange(property.name, parseFloat(e.target.value) || 0)}
          />
        );

      case 'boolean':
        return (
          <Switch
            checked={!!value}
            onCheckedChange={(checked) => handleParameterChange(property.name, checked)}
          />
        );

      case 'select':
        return (
          <Select
            value={value || ''}
            onValueChange={(newValue) => handleParameterChange(property.name, newValue)}
          >
            <SelectTrigger className={hasError ? 'border-red-500' : ''}>
              <SelectValue placeholder={property.placeholder || 'Select an option'} />
            </SelectTrigger>
            <SelectContent>
              {property.options?.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  <div>
                    <div>{option.label}</div>
                    {option.description && (
                      <div className="text-xs text-gray-500">{option.description}</div>
                    )}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case 'multiselect':
        const selectedValues = Array.isArray(value) ? value : [];
        return (
          <div className="space-y-2">
            {property.options?.map((option) => (
              <div key={option.value} className="flex items-center space-x-2">
                <Switch
                  checked={selectedValues.includes(option.value)}
                  onCheckedChange={(checked) => {
                    const newValues = checked
                      ? [...selectedValues, option.value]
                      : selectedValues.filter(v => v !== option.value);
                    handleParameterChange(property.name, newValues);
                  }}
                />
                <Label>{option.label}</Label>
              </div>
            ))}
          </div>
        );

      case 'json':
        return (
          <JsonEditor
            value={value}
            onChange={(newValue) => handleParameterChange(property.name, newValue)}
            height={property.rows ? property.rows * 20 : 100}
            hasError={hasError}
          />
        );

      case 'code':
        return (
          <CodeEditor
            value={value || ''}
            onChange={(newValue) => handleParameterChange(property.name, newValue)}
            language={property.language || 'javascript'}
            height={property.rows ? property.rows * 20 : 200}
            hasError={hasError}
          />
        );

      case 'credential':
        return (
          <CredentialSelector
            value={value}
            credentialType={property.name}
            onChange={(newValue) => handleParameterChange(property.name, newValue)}
            hasError={hasError}
          />
        );

      case 'file':
        return (
          <FileUpload
            value={value}
            onChange={(newValue) => handleParameterChange(property.name, newValue)}
            accept={property.accept}
            multiple={property.multiple}
            hasError={hasError}
          />
        );

      default:
        return (
          <Textarea
            {...commonProps}
            rows={property.rows || 3}
            onChange={(e) => handleParameterChange(property.name, e.target.value)}
          />
        );
    }
  };

  const groupedProperties = nodeType.properties.reduce((groups, property) => {
    const group = property.name === 'name' || property.name === 'description' ? 'basic' : 'advanced';
    if (!groups[group]) groups[group] = [];
    groups[group].push(property);
    return groups;
  }, {} as Record<string, NodeProperty[]>);

  return (
    <div className="fixed inset-y-0 right-0 w-96 bg-white shadow-lg border-l z-50 overflow-hidden">
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center space-x-2">
            <div className="text-lg font-semibold">{nodeType.name}</div>
            <Badge variant="secondary">{nodeType.version}</Badge>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4">
          <div className="space-y-4">
            {/* Node Info */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Node Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="text-sm text-gray-600">{nodeType.description}</div>
                <div className="flex flex-wrap gap-1">
                  {nodeType.tags?.map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Configuration */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Configuration</CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="basic" className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="basic">Basic</TabsTrigger>
                    <TabsTrigger value="advanced">Advanced</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="basic" className="space-y-4 mt-4">
                    {groupedProperties.basic?.map((property) => (
                      <div key={property.name} className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Label htmlFor={property.name} className="text-sm font-medium">
                            {property.displayName}
                            {property.required && <span className="text-red-500 ml-1">*</span>}
                          </Label>
                          {property.description && (
                            <div className="group relative">
                              <Info className="h-3 w-3 text-gray-400 cursor-help" />
                              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                                {property.description}
                              </div>
                            </div>
                          )}
                        </div>
                        {renderPropertyInput(property)}
                        {errors[property.name] && (
                          <Alert variant="destructive" className="py-2">
                            <AlertCircle className="h-3 w-3" />
                            <AlertDescription className="text-xs">
                              {errors[property.name]}
                            </AlertDescription>
                          </Alert>
                        )}
                      </div>
                    ))}
                  </TabsContent>
                  
                  <TabsContent value="advanced" className="space-y-4 mt-4">
                    {groupedProperties.advanced?.map((property) => (
                      <div key={property.name} className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Label htmlFor={property.name} className="text-sm font-medium">
                            {property.displayName}
                            {property.required && <span className="text-red-500 ml-1">*</span>}
                          </Label>
                          {property.description && (
                            <div className="group relative">
                              <Info className="h-3 w-3 text-gray-400 cursor-help" />
                              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                                {property.description}
                              </div>
                            </div>
                          )}
                        </div>
                        {renderPropertyInput(property)}
                        {errors[property.name] && (
                          <Alert variant="destructive" className="py-2">
                            <AlertCircle className="h-3 w-3" />
                            <AlertDescription className="text-xs">
                              {errors[property.name]}
                            </AlertDescription>
                          </Alert>
                        )}
                      </div>
                    ))}
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>

            {/* Documentation */}
            {nodeType.documentation?.examples && (
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm">Examples</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {nodeType.documentation.examples.map((example, index) => (
                    <div key={index} className="border rounded p-3">
                      <div className="font-medium text-sm">{example.title}</div>
                      <div className="text-xs text-gray-600 mt-1">{example.description}</div>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-2"
                        onClick={() => setParameters({ ...parameters, ...example.configuration })}
                      >
                        Use Example
                      </Button>
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="border-t p-4 flex space-x-2">
          <Button variant="outline" onClick={onClose} className="flex-1">
            Cancel
          </Button>
          <Button onClick={handleSave} className="flex-1">
            <Save className="h-4 w-4 mr-2" />
            Save
          </Button>
        </div>
      </div>
    </div>
  );
};
