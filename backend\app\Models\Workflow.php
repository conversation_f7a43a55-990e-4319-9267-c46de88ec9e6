<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Workflow extends Model
{
    protected $fillable = [
        'name',
        'description',
        'version',
        'is_active',
        'nodes',
        'edges',
        'metadata',
        'category',
        'complexity',
        'estimated_time',
        'template_id',
        'user_id',
        'last_executed_at',
        'execution_count',
        'settings',
        'tags',
        'is_template',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_template' => 'boolean',
        'nodes' => 'array',
        'edges' => 'array',
        'metadata' => 'array',
        'settings' => 'array',
        'tags' => 'array',
        'last_executed_at' => 'datetime',
        'execution_count' => 'integer',
    ];

    public function template(): BelongsTo
    {
        return $this->belongsTo(WorkflowTemplate::class, 'template_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function triggers(): HasMany
    {
        return $this->hasMany(WorkflowTrigger::class);
    }

    public function executions(): HasMany
    {
        return $this->hasMany(WorkflowExecution::class);
    }

    public function credentials(): HasMany
    {
        return $this->hasMany(WorkflowCredential::class);
    }

    // Scope for active workflows
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // Scope for templates
    public function scopeTemplates($query)
    {
        return $query->where('is_template', true);
    }

    // Scope by category
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    // Scope by tags
    public function scopeByTags($query, array $tags)
    {
        return $query->where(function ($q) use ($tags) {
            foreach ($tags as $tag) {
                $q->orWhereJsonContains('tags', $tag);
            }
        });
    }

    // Scope for workflows by category
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }
}
