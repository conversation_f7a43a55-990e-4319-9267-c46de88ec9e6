<?php

namespace App\Services;

use App\Events\WorkflowExecutionEvent;
use Illuminate\Support\Facades\Log;

class ExecutionEventBroadcaster
{
    protected static $instance;
    protected $webSocketServer;

    protected function __construct()
    {
        // In a real implementation, this would connect to the running WebSocket server
        // For now, we'll use <PERSON><PERSON>'s broadcasting system
    }

    public static function getInstance(): self
    {
        if (!self::$instance) {
            self::$instance = new self();
        }
        
        return self::$instance;
    }

    public function broadcastWorkflowStarted(string $executionId, string $workflowId, array $workflowData = []): void
    {
        $this->broadcast($executionId, $workflowId, 'workflow_started', [
            'workflow' => $workflowData,
        ]);
    }

    public function broadcastWorkflowCompleted(string $executionId, string $workflowId, array $result = []): void
    {
        $this->broadcast($executionId, $workflowId, 'workflow_completed', [
            'result' => $result,
            'duration' => $result['duration'] ?? 0,
            'nodesExecuted' => $result['nodesExecuted'] ?? 0,
        ]);
    }

    public function broadcastWorkflowFailed(string $executionId, string $workflowId, array $error = []): void
    {
        $this->broadcast($executionId, $workflowId, 'workflow_failed', [
            'error' => $error,
        ]);
    }

    public function broadcastNodeStarted(string $executionId, string $workflowId, string $nodeId, array $nodeData = []): void
    {
        $this->broadcast($executionId, $workflowId, 'node_started', [
            'node' => $nodeData,
        ], $nodeId);
    }

    public function broadcastNodeCompleted(string $executionId, string $workflowId, string $nodeId, array $nodeData = [], array $result = []): void
    {
        $this->broadcast($executionId, $workflowId, 'node_completed', [
            'node' => $nodeData,
            'result' => $result,
        ], $nodeId);
    }

    public function broadcastNodeFailed(string $executionId, string $workflowId, string $nodeId, array $nodeData = [], array $error = []): void
    {
        $this->broadcast($executionId, $workflowId, 'node_failed', [
            'node' => $nodeData,
            'error' => $error,
        ], $nodeId);
    }

    public function broadcastExecutionProgress(
        string $executionId, 
        string $workflowId, 
        int $completedNodes, 
        int $totalNodes, 
        ?string $currentNode = null
    ): void {
        $percentage = $totalNodes > 0 ? ($completedNodes / $totalNodes) * 100 : 0;
        
        $this->broadcast($executionId, $workflowId, 'execution_progress', [
            'completedNodes' => $completedNodes,
            'totalNodes' => $totalNodes,
            'currentNode' => $currentNode,
            'percentage' => round($percentage, 2),
        ]);
    }

    protected function broadcast(
        string $executionId, 
        string $workflowId, 
        string $eventType, 
        array $data = [], 
        ?string $nodeId = null
    ): void {
        try {
            // Broadcast using Laravel's event system
            event(new WorkflowExecutionEvent(
                $executionId,
                $workflowId,
                $eventType,
                $data,
                $nodeId
            ));

            Log::info('Execution event broadcasted', [
                'executionId' => $executionId,
                'workflowId' => $workflowId,
                'eventType' => $eventType,
                'nodeId' => $nodeId,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to broadcast execution event', [
                'executionId' => $executionId,
                'workflowId' => $workflowId,
                'eventType' => $eventType,
                'error' => $e->getMessage(),
            ]);
        }
    }

    public function setWebSocketServer($server): void
    {
        $this->webSocketServer = $server;
    }

    public function broadcastToWebSocket(string $executionId, string $workflowId, array $event): void
    {
        if ($this->webSocketServer) {
            try {
                $this->webSocketServer->broadcastExecutionEvent($executionId, $workflowId, $event);
            } catch (\Exception $e) {
                Log::error('Failed to broadcast to WebSocket server', [
                    'executionId' => $executionId,
                    'workflowId' => $workflowId,
                    'error' => $e->getMessage(),
                ]);
            }
        }
    }
}
