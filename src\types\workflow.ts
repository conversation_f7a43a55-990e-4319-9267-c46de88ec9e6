// Enhanced Workflow Type Definitions
// This file contains comprehensive type definitions for the enhanced workflow system

export interface ValidationRule {
  type: 'required' | 'min' | 'max' | 'pattern' | 'custom';
  value?: any;
  message: string;
  validator?: (value: any) => boolean;
}

export interface NodeInput {
  name: string;
  type: 'any' | 'string' | 'number' | 'boolean' | 'object' | 'array';
  required: boolean;
  description: string;
  multiple?: boolean;
}

export interface NodeOutput {
  name: string;
  type: 'any' | 'string' | 'number' | 'boolean' | 'object' | 'array';
  description: string;
}

export interface NodeProperty {
  name: string;
  displayName: string;
  type: 'string' | 'number' | 'boolean' | 'select' | 'multiselect' | 'json' | 'code' | 'credential' | 'file';
  required: boolean;
  default?: any;
  options?: Array<{label: string, value: any, description?: string}>;
  description: string;
  placeholder?: string;
  validation?: ValidationRule[];
  dependsOn?: string; // Property name this depends on
  condition?: (values: Record<string, any>) => boolean;
  
  // UI specific properties
  rows?: number; // For textarea/code editor
  language?: string; // For code editor
  accept?: string; // For file input
  multiple?: boolean; // For file/select inputs
}

export interface RetryPolicy {
  enabled: boolean;
  maxAttempts: number;
  delay: number; // milliseconds
  backoffMultiplier?: number;
  maxDelay?: number;
}

export interface EnhancedNodeType {
  id: string;
  name: string;
  type: 'trigger' | 'action' | 'logic' | 'output' | 'integration';
  category: string;
  subcategory?: string;
  label: string;
  description: string;
  icon: string; // Icon name or component
  version: string;
  
  // Enhanced configuration
  properties: NodeProperty[];
  inputs: NodeInput[];
  outputs: NodeOutput[];
  
  // Execution configuration
  retryPolicy?: RetryPolicy;
  timeout?: number; // milliseconds
  
  // UI configuration
  color: string;
  backgroundColor: string;
  borderColor: string;
  
  // Documentation
  documentation?: {
    url?: string;
    examples?: Array<{
      title: string;
      description: string;
      configuration: Record<string, any>;
    }>;
  };
  
  // Metadata
  tags?: string[];
  author?: string;
  license?: string;
  deprecated?: boolean;
  experimental?: boolean;
}

export interface NodeCredential {
  id: string;
  name: string;
  type: string;
  properties: NodeProperty[];
  testable?: boolean;
  testEndpoint?: string;
}

export interface WorkflowNode {
  id: string;
  type: string; // References EnhancedNodeType.id
  name: string;
  position: { x: number; y: number };
  
  // Configuration
  parameters: Record<string, any>;
  credentials?: Record<string, string>; // credential type -> credential id
  
  // Execution state
  disabled?: boolean;
  continueOnFail?: boolean;
  retryOnFail?: boolean;
  waitBetween?: number;
  
  // UI state
  selected?: boolean;
  dragging?: boolean;
  
  // Metadata
  notes?: string;
  color?: string;
}

export interface WorkflowEdge {
  id: string;
  source: string;
  target: string;
  sourceHandle?: string;
  targetHandle?: string;
  
  // Conditional execution
  condition?: {
    enabled: boolean;
    expression: string;
  };
  
  // UI properties
  animated?: boolean;
  style?: Record<string, any>;
  label?: string;
}

export interface ExecutionContext {
  workflowId: string;
  executionId: string;
  userId?: string;
  
  // Runtime data
  variables: Record<string, any>;
  credentials: Record<string, any>;
  
  // Execution settings
  settings: {
    timezone?: string;
    timeout?: number;
    saveExecutionProgress?: boolean;
    saveDataErrorExecution?: 'all' | 'none';
    saveDataSuccessExecution?: 'all' | 'none';
  };
  
  // Current state
  currentNodeId?: string;
  startTime: Date;
  endTime?: Date;
  status: 'running' | 'success' | 'error' | 'cancelled' | 'waiting';
}

export interface NodeExecutionResult {
  success: boolean;
  data?: any;
  error?: {
    message: string;
    stack?: string;
    code?: string;
  };
  executionTime: number;
  outputData?: Record<string, any>;
}

export interface WorkflowExecutionResult {
  success: boolean;
  executionId: string;
  startTime: Date;
  endTime: Date;
  duration: number;
  
  // Node results
  nodeResults: Record<string, NodeExecutionResult>;
  
  // Final output
  data?: any;
  error?: {
    message: string;
    nodeId?: string;
    stack?: string;
  };
  
  // Statistics
  nodesExecuted: number;
  totalNodes: number;
}

// Trigger System Types
export interface TriggerConfiguration {
  [key: string]: any;
}

export interface WebhookTriggerConfig extends TriggerConfiguration {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  path: string;
  authentication?: 'none' | 'basic' | 'header' | 'query';
  authenticationProperty?: string;
  responseMode?: 'onReceived' | 'lastNode';
  responseData?: string;
}

export interface ScheduleTriggerConfig extends TriggerConfiguration {
  rule: string; // Cron expression
  timezone?: string;
}

export interface ManualTriggerConfig extends TriggerConfiguration {
  // No specific configuration needed
}

export interface FileTriggerConfig extends TriggerConfiguration {
  watchPath: string;
  events: Array<'created' | 'modified' | 'deleted'>;
  filePattern?: string;
}

export interface EmailTriggerConfig extends TriggerConfiguration {
  imapHost: string;
  imapPort: number;
  username: string;
  password: string;
  folder?: string;
  markAsRead?: boolean;
}

export interface WorkflowTrigger {
  id: string;
  type: 'webhook' | 'schedule' | 'manual' | 'file' | 'email' | 'database';
  workflowId: string;
  nodeId: string;
  configuration: TriggerConfiguration;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  lastTriggered?: Date;
  triggerCount: number;
}

export interface TriggerEvent {
  triggerId: string;
  workflowId: string;
  payload: any;
  timestamp: Date;
  source: string;
  metadata?: Record<string, any>;
}

// Real-time execution types
export interface ExecutionEvent {
  type: 'workflow_started' | 'workflow_completed' | 'workflow_failed' | 'node_started' | 'node_completed' | 'node_failed' | 'execution_progress';
  executionId: string;
  workflowId: string;
  nodeId?: string;
  timestamp: Date;
  data: any;
}

export interface WorkflowExecutionSubscription {
  executionId: string;
  workflowId: string;
  userId: string;
  callback: (event: ExecutionEvent) => void;
}

export interface ExecutionProgress {
  executionId: string;
  status: 'running' | 'success' | 'error' | 'cancelled';
  completedNodes: number;
  totalNodes: number;
  currentNode?: string;
  percentage: number;
  duration: number;
  estimatedTimeRemaining?: number;
}

export interface WebSocketMessage {
  type: 'subscribe' | 'unsubscribe' | 'execution_event' | 'error';
  executionId?: string;
  workflowId?: string;
  event?: ExecutionEvent;
  error?: string;
}

// Workflow Execution Engine Types
export interface WorkflowExecutor {
  execute(workflow: EnhancedWorkflow, context: ExecutionContext): Promise<WorkflowExecutionResult>;
  executeNode(node: WorkflowNode, input: any, context: ExecutionContext): Promise<NodeExecutionResult>;
  handleError(error: Error, node: WorkflowNode, context: ExecutionContext): Promise<void>;
  retry(node: WorkflowNode, context: ExecutionContext): Promise<NodeExecutionResult>;
  validateWorkflow(workflow: EnhancedWorkflow): ValidationResult;
  subscribeToExecution(executionId: string, callback: (event: ExecutionEvent) => void): () => void;
  getExecutionProgress(executionId: string): Promise<ExecutionProgress | null>;
}

export interface ValidationResult {
  valid: boolean;
  errors: Array<{
    nodeId?: string;
    message: string;
    type: 'error' | 'warning';
  }>;
}

export interface EnhancedWorkflow {
  id: string;
  name: string;
  description?: string;
  version: string;

  // Structure
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];

  // Configuration
  settings: {
    timezone?: string;
    timeout?: number;
    errorWorkflow?: string;
    saveExecutionProgress?: boolean;
    saveDataErrorExecution?: 'all' | 'none';
    saveDataSuccessExecution?: 'all' | 'none';
  };

  // Metadata
  tags?: string[];
  category?: string;
  isActive: boolean;
  isTemplate?: boolean;

  // Triggers
  triggers: WorkflowTrigger[];

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  lastExecuted?: Date;
  executionCount: number;
}
