<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('node_executions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('workflow_execution_id')->constrained()->onDelete('cascade');
            $table->string('node_id');
            $table->string('node_type');
            $table->string('node_name');
            $table->enum('status', ['running', 'success', 'error']);
            $table->timestamp('started_at');
            $table->timestamp('finished_at')->nullable();
            $table->integer('duration')->nullable(); // in milliseconds
            $table->json('input_data')->nullable();
            $table->json('output_data')->nullable();
            $table->text('error_message')->nullable();
            $table->integer('retry_count')->default(0);
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->index(['workflow_execution_id', 'node_id']);
            $table->index(['status', 'started_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('node_executions');
    }
};
