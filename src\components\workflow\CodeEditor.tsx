import React, { useRef, useEffect } from 'react';
import { Card } from '@/components/ui/card';

interface CodeEditorProps {
  value: string;
  onChange: (value: string) => void;
  language?: string;
  height?: number;
  hasError?: boolean;
  readOnly?: boolean;
}

export const CodeEditor: React.FC<CodeEditorProps> = ({
  value,
  onChange,
  language = 'javascript',
  height = 200,
  hasError = false,
  readOnly = false,
}) => {
  const editorRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    if (editorRef.current) {
      editorRef.current.value = value || '';
    }
  }, [value]);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value);
  };

  const handleKeyDown = (e: React.KeyEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Tab') {
      e.preventDefault();
      const textarea = e.currentTarget;
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const newValue = value.substring(0, start) + '  ' + value.substring(end);
      onChange(newValue);
      
      // Set cursor position after the inserted spaces
      setTimeout(() => {
        textarea.selectionStart = textarea.selectionEnd = start + 2;
      }, 0);
    }
  };

  return (
    <Card className={`p-0 ${hasError ? 'border-red-500' : ''}`}>
      <div className="relative">
        {/* Language indicator */}
        <div className="absolute top-2 right-2 z-10">
          <span className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded">
            {language}
          </span>
        </div>
        
        {/* Editor */}
        <textarea
          ref={editorRef}
          value={value || ''}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          readOnly={readOnly}
          className={`
            w-full p-3 font-mono text-sm border-0 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 rounded
            ${hasError ? 'border-red-500' : ''}
            ${readOnly ? 'bg-gray-50 cursor-not-allowed' : 'bg-white'}
          `}
          style={{ height: `${height}px` }}
          placeholder={`Enter ${language} code...`}
          spellCheck={false}
        />
        
        {/* Line numbers (simplified) */}
        <div className="absolute left-0 top-0 p-3 text-gray-400 text-sm font-mono pointer-events-none select-none">
          {value?.split('\n').map((_, index) => (
            <div key={index} className="leading-5">
              {index + 1}
            </div>
          ))}
        </div>
        
        {/* Syntax highlighting overlay (placeholder) */}
        <div className="absolute inset-0 pointer-events-none">
          {/* In a real implementation, this would contain syntax highlighting */}
        </div>
      </div>
      
      {/* Footer with helpful info */}
      <div className="border-t bg-gray-50 px-3 py-2 text-xs text-gray-600">
        <div className="flex justify-between items-center">
          <span>Press Tab to indent, Shift+Tab to unindent</span>
          <span>{value?.length || 0} characters</span>
        </div>
      </div>
    </Card>
  );
};
