<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class WorkflowExecutionEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $executionId;
    public $workflowId;
    public $eventType;
    public $nodeId;
    public $timestamp;
    public $data;

    /**
     * Create a new event instance.
     */
    public function __construct(
        string $executionId,
        string $workflowId,
        string $eventType,
        array $data = [],
        ?string $nodeId = null
    ) {
        $this->executionId = $executionId;
        $this->workflowId = $workflowId;
        $this->eventType = $eventType;
        $this->nodeId = $nodeId;
        $this->timestamp = now();
        $this->data = $data;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel("workflow-execution.{$this->executionId}"),
            new PrivateChannel("workflow.{$this->workflowId}"),
        ];
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith(): array
    {
        return [
            'type' => $this->eventType,
            'executionId' => $this->executionId,
            'workflowId' => $this->workflowId,
            'nodeId' => $this->nodeId,
            'timestamp' => $this->timestamp,
            'data' => $this->data,
        ];
    }

    /**
     * The event's broadcast name.
     *
     * @return string
     */
    public function broadcastAs(): string
    {
        return 'execution.event';
    }
}
