<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('workflows', function (Blueprint $table) {
            $table->string('version')->default('1.0.0')->after('description');
            $table->json('settings')->nullable()->after('metadata');
            $table->json('tags')->nullable()->after('settings');
            $table->boolean('is_template')->default(false)->after('is_active');
            
            // Add indexes for better performance
            $table->index(['is_active', 'category']);
            $table->index(['is_template', 'category']);
            $table->index('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('workflows', function (Blueprint $table) {
            $table->dropColumn(['version', 'settings', 'tags', 'is_template']);
            $table->dropIndex(['is_active', 'category']);
            $table->dropIndex(['is_template', 'category']);
            $table->dropIndex(['user_id']);
        });
    }
};
