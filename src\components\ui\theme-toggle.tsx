import React from "react";
import { <PERSON>, <PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useTheme } from "@/contexts/ThemeContext";

export const ThemeToggle: React.FC = () => {
  const { theme, setTheme } = useTheme();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon" className="bg-white dark:bg-[#003135] border-[#0FA4AF]/30 dark:border-[#024950] hover:bg-[#0FA4AF]/10 dark:hover:bg-[#024950]/50 text-[#024950] dark:text-[#AFDDE5]">
          <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
          <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="bg-white dark:bg-[#003135] border-[#0FA4AF]/30 dark:border-[#024950]">
        <DropdownMenuItem onClick={() => setTheme("light")} className="hover:bg-[#0FA4AF]/10 dark:hover:bg-[#024950]/50 text-[#003135] dark:text-[#AFDDE5]">
          <Sun className="mr-2 h-4 w-4" />
          Light
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("dark")} className="hover:bg-[#0FA4AF]/10 dark:hover:bg-[#024950]/50 text-[#003135] dark:text-[#AFDDE5]">
          <Moon className="mr-2 h-4 w-4" />
          Dark
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ThemeToggle;
