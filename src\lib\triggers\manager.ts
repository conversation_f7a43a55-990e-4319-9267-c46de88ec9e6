// Trigger Management System
// This file contains the trigger management and execution logic

import { 
  WorkflowTrigger, 
  TriggerEvent, 
  WebhookTriggerConfig, 
  ScheduleTriggerConfig, 
  ManualTriggerConfig,
  FileTriggerConfig,
  EmailTriggerConfig,
  ExecutionContext,
  EnhancedWorkflow 
} from '@/types/workflow';
import { workflowEngine } from '@/lib/execution/engine';

export interface TriggerManager {
  registerTrigger(trigger: WorkflowTrigger): Promise<void>;
  unregisterTrigger(triggerId: string): Promise<void>;
  processTrigger(triggerId: string, payload: any): Promise<void>;
  activateTrigger(triggerId: string): Promise<void>;
  deactivateTrigger(triggerId: string): Promise<void>;
  getTrigger(triggerId: string): WorkflowTrigger | undefined;
  getTriggersForWorkflow(workflowId: string): WorkflowTrigger[];
}

export class WorkflowTriggerManager implements TriggerManager {
  private static instance: WorkflowTriggerManager;
  private triggers: Map<string, WorkflowTrigger> = new Map();
  private webhookRoutes: Map<string, WorkflowTrigger> = new Map();
  private scheduleJobs: Map<string, NodeJS.Timeout> = new Map();
  private fileWatchers: Map<string, any> = new Map(); // Would use fs.watch in Node.js

  private constructor() {}

  public static getInstance(): WorkflowTriggerManager {
    if (!WorkflowTriggerManager.instance) {
      WorkflowTriggerManager.instance = new WorkflowTriggerManager();
    }
    return WorkflowTriggerManager.instance;
  }

  public async registerTrigger(trigger: WorkflowTrigger): Promise<void> {
    this.triggers.set(trigger.id, trigger);

    if (trigger.isActive) {
      await this.activateTrigger(trigger.id);
    }
  }

  public async unregisterTrigger(triggerId: string): Promise<void> {
    const trigger = this.triggers.get(triggerId);
    if (!trigger) return;

    await this.deactivateTrigger(triggerId);
    this.triggers.delete(triggerId);
  }

  public async activateTrigger(triggerId: string): Promise<void> {
    const trigger = this.triggers.get(triggerId);
    if (!trigger) throw new Error(`Trigger ${triggerId} not found`);

    switch (trigger.type) {
      case 'webhook':
        await this.activateWebhookTrigger(trigger);
        break;
      case 'schedule':
        await this.activateScheduleTrigger(trigger);
        break;
      case 'file':
        await this.activateFileTrigger(trigger);
        break;
      case 'email':
        await this.activateEmailTrigger(trigger);
        break;
      case 'manual':
        // Manual triggers don't need activation
        break;
      default:
        throw new Error(`Unsupported trigger type: ${trigger.type}`);
    }

    trigger.isActive = true;
    this.triggers.set(triggerId, trigger);
  }

  public async deactivateTrigger(triggerId: string): Promise<void> {
    const trigger = this.triggers.get(triggerId);
    if (!trigger) return;

    switch (trigger.type) {
      case 'webhook':
        this.deactivateWebhookTrigger(trigger);
        break;
      case 'schedule':
        this.deactivateScheduleTrigger(trigger);
        break;
      case 'file':
        this.deactivateFileTrigger(trigger);
        break;
      case 'email':
        this.deactivateEmailTrigger(trigger);
        break;
    }

    trigger.isActive = false;
    this.triggers.set(triggerId, trigger);
  }

  public async processTrigger(triggerId: string, payload: any): Promise<void> {
    const trigger = this.triggers.get(triggerId);
    if (!trigger || !trigger.isActive) {
      throw new Error(`Trigger ${triggerId} not found or inactive`);
    }

    // Create trigger event
    const event: TriggerEvent = {
      triggerId,
      workflowId: trigger.workflowId,
      payload,
      timestamp: new Date(),
      source: trigger.type,
      metadata: {
        triggerType: trigger.type,
        nodeId: trigger.nodeId,
      },
    };

    // Update trigger statistics
    trigger.lastTriggered = new Date();
    trigger.triggerCount++;
    this.triggers.set(triggerId, trigger);

    // Execute workflow
    await this.executeWorkflowFromTrigger(event);
  }

  public getTrigger(triggerId: string): WorkflowTrigger | undefined {
    return this.triggers.get(triggerId);
  }

  public getTriggersForWorkflow(workflowId: string): WorkflowTrigger[] {
    return Array.from(this.triggers.values()).filter(
      trigger => trigger.workflowId === workflowId
    );
  }

  // Webhook trigger methods
  private async activateWebhookTrigger(trigger: WorkflowTrigger): Promise<void> {
    const config = trigger.configuration as WebhookTriggerConfig;
    this.webhookRoutes.set(config.path, trigger);
    
    // In a real implementation, this would register the route with Express/Fastify
    console.log(`Webhook trigger activated: ${config.method} ${config.path}`);
  }

  private deactivateWebhookTrigger(trigger: WorkflowTrigger): void {
    const config = trigger.configuration as WebhookTriggerConfig;
    this.webhookRoutes.delete(config.path);
    
    console.log(`Webhook trigger deactivated: ${config.method} ${config.path}`);
  }

  public async handleWebhookRequest(
    method: string, 
    path: string, 
    payload: any, 
    headers: Record<string, string>
  ): Promise<{ success: boolean; response?: any; error?: string }> {
    const trigger = this.webhookRoutes.get(path);
    if (!trigger) {
      return { success: false, error: 'Webhook not found' };
    }

    const config = trigger.configuration as WebhookTriggerConfig;
    
    // Validate HTTP method
    if (config.method !== method) {
      return { success: false, error: `Method ${method} not allowed` };
    }

    // Handle authentication if configured
    if (config.authentication && config.authentication !== 'none') {
      const authResult = this.validateWebhookAuthentication(config, headers, payload);
      if (!authResult.valid) {
        return { success: false, error: authResult.error };
      }
    }

    try {
      // Process the trigger
      await this.processTrigger(trigger.id, {
        method,
        path,
        headers,
        body: payload,
        timestamp: new Date(),
      });

      // Return response based on response mode
      if (config.responseMode === 'onReceived') {
        return { success: true, response: { message: 'Webhook received' } };
      } else {
        // For 'lastNode' mode, we'd need to wait for workflow completion
        // This is a simplified implementation
        return { success: true, response: { message: 'Workflow triggered' } };
      }

    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  private validateWebhookAuthentication(
    config: WebhookTriggerConfig, 
    headers: Record<string, string>, 
    payload: any
  ): { valid: boolean; error?: string } {
    // Simplified authentication validation
    // In a real implementation, this would be more robust
    switch (config.authentication) {
      case 'header':
        if (!headers[config.authenticationProperty || 'authorization']) {
          return { valid: false, error: 'Missing authentication header' };
        }
        break;
      case 'basic':
        if (!headers.authorization?.startsWith('Basic ')) {
          return { valid: false, error: 'Missing or invalid basic authentication' };
        }
        break;
      case 'query':
        // Would check query parameters in a real implementation
        break;
    }
    
    return { valid: true };
  }

  // Schedule trigger methods
  private async activateScheduleTrigger(trigger: WorkflowTrigger): Promise<void> {
    const config = trigger.configuration as ScheduleTriggerConfig;
    
    // Parse cron expression and set up timer
    // This is a simplified implementation - in production, use a proper cron library
    const interval = this.parseCronExpression(config.rule);
    
    const job = setInterval(async () => {
      try {
        await this.processTrigger(trigger.id, {
          scheduledTime: new Date(),
          cronRule: config.rule,
          timezone: config.timezone,
        });
      } catch (error) {
        console.error(`Schedule trigger ${trigger.id} failed:`, error);
      }
    }, interval);

    this.scheduleJobs.set(trigger.id, job);
    console.log(`Schedule trigger activated: ${config.rule}`);
  }

  private deactivateScheduleTrigger(trigger: WorkflowTrigger): void {
    const job = this.scheduleJobs.get(trigger.id);
    if (job) {
      clearInterval(job);
      this.scheduleJobs.delete(trigger.id);
    }
    
    console.log(`Schedule trigger deactivated: ${trigger.id}`);
  }

  private parseCronExpression(cronRule: string): number {
    // Simplified cron parsing - returns interval in milliseconds
    // In production, use a proper cron library like node-cron
    
    // For demo purposes, treat "0 9 * * 1-5" as every 24 hours
    // Real implementation would properly parse cron expressions
    return 24 * 60 * 60 * 1000; // 24 hours
  }

  // File trigger methods
  private async activateFileTrigger(trigger: WorkflowTrigger): Promise<void> {
    const config = trigger.configuration as FileTriggerConfig;
    
    // In a real implementation, this would use fs.watch() in Node.js
    console.log(`File trigger activated: watching ${config.watchPath}`);
    
    // Simulate file watching
    // const watcher = fs.watch(config.watchPath, (eventType, filename) => {
    //   if (config.events.includes(eventType as any)) {
    //     this.processTrigger(trigger.id, {
    //       eventType,
    //       filename,
    //       path: config.watchPath,
    //       timestamp: new Date(),
    //     });
    //   }
    // });
    // this.fileWatchers.set(trigger.id, watcher);
  }

  private deactivateFileTrigger(trigger: WorkflowTrigger): void {
    const watcher = this.fileWatchers.get(trigger.id);
    if (watcher) {
      // watcher.close();
      this.fileWatchers.delete(trigger.id);
    }
    
    console.log(`File trigger deactivated: ${trigger.id}`);
  }

  // Email trigger methods
  private async activateEmailTrigger(trigger: WorkflowTrigger): Promise<void> {
    const config = trigger.configuration as EmailTriggerConfig;
    
    // In a real implementation, this would connect to IMAP server
    console.log(`Email trigger activated: ${config.username}@${config.imapHost}`);
    
    // Simulate email polling
    // const interval = setInterval(async () => {
    //   // Check for new emails and process them
    // }, 60000); // Check every minute
  }

  private deactivateEmailTrigger(trigger: WorkflowTrigger): void {
    console.log(`Email trigger deactivated: ${trigger.id}`);
  }

  // Workflow execution
  private async executeWorkflowFromTrigger(event: TriggerEvent): Promise<void> {
    try {
      // In a real implementation, this would fetch the workflow from the database
      const workflow = await this.getWorkflowById(event.workflowId);
      if (!workflow) {
        throw new Error(`Workflow ${event.workflowId} not found`);
      }

      // Create execution context
      const context: ExecutionContext = {
        workflowId: event.workflowId,
        executionId: `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        variables: {},
        credentials: {},
        settings: workflow.settings,
        startTime: new Date(),
        status: 'running',
      };

      // Execute the workflow
      const result = await workflowEngine.execute(workflow, context);
      
      console.log(`Workflow execution ${context.executionId} completed:`, {
        success: result.success,
        duration: result.duration,
        nodesExecuted: result.nodesExecuted,
      });

    } catch (error) {
      console.error(`Failed to execute workflow from trigger ${event.triggerId}:`, error);
    }
  }

  private async getWorkflowById(workflowId: string): Promise<EnhancedWorkflow | null> {
    // This would fetch from database in a real implementation
    // For now, return a mock workflow
    return null;
  }

  // Manual trigger execution
  public async executeManualTrigger(
    workflowId: string, 
    nodeId: string, 
    inputData?: any
  ): Promise<void> {
    const triggers = this.getTriggersForWorkflow(workflowId);
    const manualTrigger = triggers.find(t => t.type === 'manual' && t.nodeId === nodeId);
    
    if (!manualTrigger) {
      throw new Error(`Manual trigger not found for workflow ${workflowId}, node ${nodeId}`);
    }

    await this.processTrigger(manualTrigger.id, {
      manual: true,
      inputData: inputData || {},
      timestamp: new Date(),
    });
  }
}

// Export singleton instance
export const triggerManager = WorkflowTriggerManager.getInstance();
