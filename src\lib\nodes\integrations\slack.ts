import { EnhancedNodeType } from '@/types/workflow';

export const slackNodes: EnhancedNodeType[] = [
  {
    name: 'Slack Send Message',
    type: 'action',
    category: 'Communication',
    description: 'Send a message to a Slack channel or user',
    version: '1.0.0',
    icon: '💬',
    color: '#4A154B',
    tags: ['slack', 'messaging', 'communication'],
    inputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Input',
        required: false,
      },
    ],
    outputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Output',
      },
    ],
    properties: [
      {
        name: 'credential',
        displayName: 'Credential',
        type: 'credential',
        required: true,
        description: 'Slack API credentials',
      },
      {
        name: 'channel',
        displayName: 'Channel',
        type: 'string',
        required: true,
        placeholder: '#general or @username',
        description: 'Channel name or user to send message to',
      },
      {
        name: 'text',
        displayName: 'Message Text',
        type: 'string',
        required: true,
        placeholder: 'Hello from workflow!',
        description: 'The message content to send',
        rows: 3,
      },
      {
        name: 'username',
        displayName: 'Bot Username',
        type: 'string',
        required: false,
        placeholder: 'Workflow Bot',
        description: 'Custom username for the bot',
      },
      {
        name: 'iconEmoji',
        displayName: 'Icon Emoji',
        type: 'string',
        required: false,
        placeholder: ':robot_face:',
        description: 'Emoji to use as bot icon',
      },
      {
        name: 'attachments',
        displayName: 'Attachments',
        type: 'json',
        required: false,
        description: 'Rich message attachments (JSON format)',
        default: [],
      },
      {
        name: 'blocks',
        displayName: 'Blocks',
        type: 'json',
        required: false,
        description: 'Block Kit elements (JSON format)',
        default: [],
      },
    ],
    credentials: [
      {
        name: 'slackApi',
        required: true,
      },
    ],
    documentation: {
      description: 'Send messages to Slack channels or direct messages using the Slack Web API.',
      examples: [
        {
          title: 'Simple Message',
          description: 'Send a basic text message',
          configuration: {
            channel: '#general',
            text: 'Hello from the workflow!',
          },
        },
        {
          title: 'Rich Message with Attachments',
          description: 'Send a message with rich formatting',
          configuration: {
            channel: '#alerts',
            text: 'Workflow Alert',
            attachments: [
              {
                color: 'good',
                title: 'Success',
                text: 'Workflow completed successfully',
                fields: [
                  {
                    title: 'Duration',
                    value: '2.5 seconds',
                    short: true,
                  },
                ],
              },
            ],
          },
        },
      ],
    },
  },
  {
    name: 'Slack Get Channel Info',
    type: 'action',
    category: 'Communication',
    description: 'Get information about a Slack channel',
    version: '1.0.0',
    icon: 'ℹ️',
    color: '#4A154B',
    tags: ['slack', 'channel', 'info'],
    inputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Input',
        required: false,
      },
    ],
    outputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Output',
      },
    ],
    properties: [
      {
        name: 'credential',
        displayName: 'Credential',
        type: 'credential',
        required: true,
        description: 'Slack API credentials',
      },
      {
        name: 'channel',
        displayName: 'Channel',
        type: 'string',
        required: true,
        placeholder: '#general or C1234567890',
        description: 'Channel name or ID',
      },
    ],
    credentials: [
      {
        name: 'slackApi',
        required: true,
      },
    ],
  },
  {
    name: 'Slack Upload File',
    type: 'action',
    category: 'Communication',
    description: 'Upload a file to Slack',
    version: '1.0.0',
    icon: '📎',
    color: '#4A154B',
    tags: ['slack', 'file', 'upload'],
    inputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Input',
        required: false,
      },
    ],
    outputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Output',
      },
    ],
    properties: [
      {
        name: 'credential',
        displayName: 'Credential',
        type: 'credential',
        required: true,
        description: 'Slack API credentials',
      },
      {
        name: 'channels',
        displayName: 'Channels',
        type: 'string',
        required: true,
        placeholder: '#general,#random',
        description: 'Comma-separated list of channels to upload to',
      },
      {
        name: 'file',
        displayName: 'File',
        type: 'file',
        required: true,
        description: 'File to upload',
      },
      {
        name: 'filename',
        displayName: 'Filename',
        type: 'string',
        required: false,
        placeholder: 'document.pdf',
        description: 'Custom filename for the upload',
      },
      {
        name: 'title',
        displayName: 'Title',
        type: 'string',
        required: false,
        placeholder: 'Important Document',
        description: 'Title for the file',
      },
      {
        name: 'initialComment',
        displayName: 'Initial Comment',
        type: 'string',
        required: false,
        placeholder: 'Here is the file you requested',
        description: 'Comment to add with the file',
        rows: 2,
      },
    ],
    credentials: [
      {
        name: 'slackApi',
        required: true,
      },
    ],
  },
  {
    name: 'Slack Create Channel',
    type: 'action',
    category: 'Communication',
    description: 'Create a new Slack channel',
    version: '1.0.0',
    icon: '➕',
    color: '#4A154B',
    tags: ['slack', 'channel', 'create'],
    inputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Input',
        required: false,
      },
    ],
    outputs: [
      {
        name: 'main',
        type: 'main',
        displayName: 'Output',
      },
    ],
    properties: [
      {
        name: 'credential',
        displayName: 'Credential',
        type: 'credential',
        required: true,
        description: 'Slack API credentials',
      },
      {
        name: 'name',
        displayName: 'Channel Name',
        type: 'string',
        required: true,
        placeholder: 'new-channel',
        description: 'Name of the channel to create',
        validation: [
          {
            type: 'pattern',
            value: '^[a-z0-9-_]+$',
            message: 'Channel name must contain only lowercase letters, numbers, hyphens, and underscores',
          },
        ],
      },
      {
        name: 'isPrivate',
        displayName: 'Private Channel',
        type: 'boolean',
        required: false,
        default: false,
        description: 'Create as private channel',
      },
      {
        name: 'purpose',
        displayName: 'Purpose',
        type: 'string',
        required: false,
        placeholder: 'Channel for discussing...',
        description: 'Purpose of the channel',
        rows: 2,
      },
      {
        name: 'topic',
        displayName: 'Topic',
        type: 'string',
        required: false,
        placeholder: 'Current topic',
        description: 'Topic for the channel',
      },
    ],
    credentials: [
      {
        name: 'slackApi',
        required: true,
      },
    ],
  },
];
